import {
  ApolloClient,
  InMemoryCache,
  createHttpLink,
  from,
} from "@apollo/client";
import { onError } from "@apollo/client/link/error";
import { setContext } from "@apollo/client/link/context";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { router } from "expo-router";
import { jwtDecode } from "jwt-decode";

// Determine API URL based on environment
const API_URL = "https://talisiapos.duckdns.org/graphql";

interface JwtPayload {
  exp?: number;
}

// HTTP link
const httpLink = createHttpLink({
  uri: API_URL,
  credentials: "include", // Send cookies for session-based auth
});

// Auth link to add JWT and X-Mobile-App header
const authLink = setContext(async (_, { headers }) => {
  const token = await AsyncStorage.getItem("token");

  if (token) {
    try {
      const payload: JwtPayload = jwtDecode(token);
      if (payload.exp && payload.exp * 1000 < Date.now()) {
        console.log("Token expired, removing...");
        await AsyncStorage.removeItem("token");
        return {
          headers: {
            ...headers,
            "X-Mobile-App": "talisia",
          },
        };
      }
    } catch (e) {
      console.error("Invalid token, removing...", e);
      await AsyncStorage.removeItem("token");
      return {
        headers: {
          ...headers,
          "X-Mobile-App": "talisia",
        },
      };
    }
  }

  return {
    headers: {
      ...headers,
      "X-Mobile-App": "talisia",
      authorization: token ? `Bearer ${token}` : "",
    },
  };
});

// Error link for handling GraphQL and network errors
const errorLink = onError(({ graphQLErrors, networkError, operation }) => {
  // Skip error handling for logout operation or if we're already on the login screen
  if (operation.operationName === "Logout") {
    return;
  }

  if (graphQLErrors) {
    graphQLErrors.forEach(({ message, locations, path }) => {
      console.error(
        `[GraphQL error]: Message: ${message}, Location: ${JSON.stringify(
          locations
        )}, Path: ${path}`
      );
      if (message.includes("not authenticated")) {
        AsyncStorage.removeItem("token")
          .then(() => client.resetStore())
          .then(() => router.replace("/login"))
          .catch((err) => console.error("Error handling auth error:", err));
      }
    });
  }

  if (networkError) {
    console.error(`[Network error]: ${networkError}`);
  }
});

// Apollo Client
const client = new ApolloClient({
  link: from([authLink, errorLink, httpLink]),
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      fetchPolicy: "network-only",
      errorPolicy: "ignore",
    },
    query: {
      fetchPolicy: "network-only",
      errorPolicy: "all",
    },
  },
});

export { client };
