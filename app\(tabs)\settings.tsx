import { useState } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from "react-native";
import {
  Text,
  Avatar,
  Button,
  TextInput,
  Portal,
  Modal,
} from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useApolloClient } from "@apollo/client";
import {
  useMeQuery,
  useEditUserMutation,
  useChangePasswordMutation,
  useLogoutMutation,
} from "../../generated/graphql";
import * as ImagePicker from "expo-image-picker";
import * as Crypto from "expo-crypto";
import supabase from "../../supabase";
import * as FileSystem from "expo-file-system";

export default function SettingsScreen() {
  const router = useRouter();
  const client = useApolloClient();
  const { data, refetch } = useMeQuery({
    fetchPolicy: "network-only",
  });
  const [editUser] = useEditUserMutation();
  const [uploading, setUploading] = useState(false);
  const [oldImagePath, setOldImagePath] = useState<string | null>(null);
  const [changePassword] = useChangePasswordMutation();

  // Edit states
  const [isEditing, setIsEditing] = useState(false);
  const [editedValues, setEditedValues] = useState({
    firstname: "",
    lastname: "",
    email: "",
    phone: "",
  });

  // Password change states
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const [logout] = useLogoutMutation();

  const handleLogout = async () => {
    try {
      // Navigate immediately to prevent any GraphQL operations
      router.replace("/login");

      // Then perform cleanup operations
      await AsyncStorage.removeItem("token");
      await client.resetStore();

      // Execute the logout mutation last
      await logout();
    } catch (err) {
      console.error("Logout error:", err);
    }
  };

  const handleEditProfile = () => {
    setIsEditing(true);
    setEditedValues({
      firstname: data?.me?.firstname || "",
      lastname: data?.me?.lastname || "",
      email: data?.me?.email || "",
      phone: data?.me?.phone || "",
    });
  };

  const handleSaveProfile = async () => {
    try {
      if (!data?.me?.id) return;

      const result = await editUser({
        variables: {
          id: data.me.id,
          params: {
            firstname: editedValues.firstname,
            middlename: data.me.middlename || "",
            lastname: editedValues.lastname,
            email: editedValues.email,
            phone: editedValues.phone,
            image: data.me.image || "",
          },
        },
      });

      if (result.data?.editUser.status) {
        Alert.alert("Success", "Profile updated successfully!");
        setIsEditing(false);
      } else {
        Alert.alert(
          "Error",
          result.data?.editUser.error?.message || "Failed to update profile"
        );
      }
    } catch (error: any) {
      console.error("Error updating profile:", error);
      Alert.alert("Error", "Failed to update profile");
    }
  };

  const handleChangePassword = async () => {
    if (newPassword !== confirmPassword) {
      setPasswordError("Passwords do not match");
      return;
    }

    try {
      const result = await changePassword({
        variables: {
          currentPassword,
          newPassword,
        },
      });

      if (result.data?.changePassword.status) {
        setPasswordModalVisible(false);
        setCurrentPassword("");
        setNewPassword("");
        setConfirmPassword("");
        setPasswordError("");
      } else {
        setPasswordError(
          result.data?.changePassword.error?.message || "Password change failed"
        );
      }
    } catch (err) {
      setPasswordError("An error occurred while changing password");
    }
  };

  const pickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0] && data?.me?.id) {
        try {
          setUploading(true);
          const selectedFile = result.assets[0];

          // Get the file extension from the URI
          const fileExtension = selectedFile.uri.split(".").pop() || "jpg";
          const mimeType = selectedFile.type || "image/jpeg";

          // Create filename with extension
          const fileName = `public/${
            data.me.id
          }_${Crypto.randomUUID()}.${fileExtension}`;

          // Read file as binary data
          const fileData = await FileSystem.readAsStringAsync(
            selectedFile.uri,
            {
              encoding: FileSystem.EncodingType.Base64,
            }
          );

          // Convert base64 to ArrayBuffer for Supabase
          const byteCharacters = atob(fileData);
          const byteNumbers = new Array(byteCharacters.length);
          for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
          }
          const byteArray = new Uint8Array(byteNumbers);

          // Upload to Supabase
          const { data: uploadedFile, error: uploadError } =
            await supabase.storage.from("heal").upload(fileName, byteArray, {
              contentType: mimeType,
              upsert: true,
            });

          if (uploadError) {
            console.error("Upload error:", uploadError);
            throw uploadError;
          }

          if (uploadedFile) {
            // Get the public URL
            const {
              data: { publicUrl },
            } = supabase.storage.from("heal").getPublicUrl(fileName);

            // Update user profile with new image URL
            const { data: userData } = await editUser({
              variables: {
                id: data.me.id,
                params: {
                  image: publicUrl,
                  firstname: data.me.firstname || "",
                  middlename: data.me.middlename || "",
                  lastname: data.me.lastname || "",
                  email: data.me.email || "",
                  phone: data.me.phone || "",
                },
              },
            });

            if (userData?.editUser.error || !userData?.editUser?.status) {
              throw new Error("Failed to update profile image");
            }

            // Delete old image if exists
            if (oldImagePath) {
              await supabase.storage.from("heal").remove([oldImagePath]);
            }

            setOldImagePath(fileName);
            await refetch(); // Refresh user data to show new image
            Alert.alert("Success", "Profile image updated successfully!");
          }
        } catch (error: any) {
          console.error("Error uploading image:", error);
          Alert.alert("Error", error.message || "Failed to upload image");
        } finally {
          setUploading(false);
        }
      }
    } catch (error: any) {
      console.error("Error picking image:", error);
      Alert.alert("Error", error.message || "Failed to pick image");
    }
  };

  return (
    <ScrollView style={styles.container}>
      {/* Profile Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={pickImage}
          style={styles.avatarContainer}
          disabled={uploading}
        >
          {data?.me?.image ? (
            <Avatar.Image size={100} source={{ uri: data.me.image }} />
          ) : (
            <Avatar.Text
              size={100}
              label={`${data?.me?.firstname?.[0] || ""}${
                data?.me?.lastname?.[0] || ""
              }`}
            />
          )}
          <View style={styles.editAvatarButton}>
            {uploading ? (
              <MaterialCommunityIcons name="loading" size={20} color="white" />
            ) : (
              <MaterialCommunityIcons name="camera" size={20} color="white" />
            )}
          </View>
        </TouchableOpacity>

        <Text style={styles.name}>
          {data?.me?.firstname} {data?.me?.lastname}
        </Text>
        <Text style={styles.role}>{data?.me?.role?.name}</Text>
      </View>

      {/* Profile Information */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Personal Information</Text>
          {!isEditing && (
            <TouchableOpacity onPress={handleEditProfile}>
              <MaterialCommunityIcons name="pencil" size={24} color="#007AFF" />
            </TouchableOpacity>
          )}
        </View>

        {isEditing ? (
          <View style={styles.editForm}>
            <TextInput
              label="First Name"
              value={editedValues.firstname}
              onChangeText={(text) =>
                setEditedValues({ ...editedValues, firstname: text })
              }
              style={styles.input}
            />
            <TextInput
              label="Last Name"
              value={editedValues.lastname}
              onChangeText={(text) =>
                setEditedValues({ ...editedValues, lastname: text })
              }
              style={styles.input}
            />
            <TextInput
              label="Email"
              value={editedValues.email}
              onChangeText={(text) =>
                setEditedValues({ ...editedValues, email: text })
              }
              style={styles.input}
            />
            <TextInput
              label="Phone"
              value={editedValues.phone}
              onChangeText={(text) =>
                setEditedValues({ ...editedValues, phone: text })
              }
              style={styles.input}
            />
            <View style={styles.editButtons}>
              <Button
                mode="outlined"
                onPress={() => setIsEditing(false)}
                style={styles.cancelButton}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={handleSaveProfile}
                style={styles.saveButton}
              >
                Save
              </Button>
            </View>
          </View>
        ) : (
          <View style={styles.infoList}>
            <InfoItem
              icon="account"
              label="First Name"
              value={data?.me?.firstname}
            />
            <InfoItem
              icon="account"
              label="Last Name"
              value={data?.me?.lastname}
            />
            <InfoItem
              icon="email"
              label="Email"
              value={data?.me?.email ? data.me.email : undefined}
            />
            <InfoItem icon="phone" label="Phone" value={data?.me?.phone} />
          </View>
        )}
      </View>

      {/* Security Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Security</Text>
        <Button
          mode="outlined"
          onPress={() => setPasswordModalVisible(true)}
          icon="lock"
          style={styles.securityButton}
        >
          Change Password
        </Button>
      </View>

      {/* Logout Button */}
      <Button
        mode="contained"
        onPress={handleLogout}
        icon="logout"
        style={styles.logoutButton}
        contentStyle={styles.logoutButtonContent}
      >
        Logout
      </Button>

      {/* Password Change Modal */}
      <Portal>
        <Modal
          visible={passwordModalVisible}
          onDismiss={() => setPasswordModalVisible(false)}
          contentContainerStyle={styles.modal}
        >
          <Text style={styles.modalTitle}>Change Password</Text>
          <TextInput
            label="Current Password"
            value={currentPassword}
            onChangeText={setCurrentPassword}
            secureTextEntry={!showPasswords.current}
            right={
              <TextInput.Icon
                icon={showPasswords.current ? "eye-off" : "eye"}
                onPress={() =>
                  setShowPasswords({
                    ...showPasswords,
                    current: !showPasswords.current,
                  })
                }
              />
            }
            style={styles.input}
          />
          <TextInput
            label="New Password"
            value={newPassword}
            onChangeText={setNewPassword}
            secureTextEntry={!showPasswords.new}
            right={
              <TextInput.Icon
                icon={showPasswords.new ? "eye-off" : "eye"}
                onPress={() =>
                  setShowPasswords({
                    ...showPasswords,
                    new: !showPasswords.new,
                  })
                }
              />
            }
            style={styles.input}
          />
          <TextInput
            label="Confirm Password"
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry={!showPasswords.confirm}
            right={
              <TextInput.Icon
                icon={showPasswords.confirm ? "eye-off" : "eye"}
                onPress={() =>
                  setShowPasswords({
                    ...showPasswords,
                    confirm: !showPasswords.confirm,
                  })
                }
              />
            }
            style={styles.input}
          />
          {passwordError ? (
            <Text style={styles.errorText}>{passwordError}</Text>
          ) : null}
          <View style={styles.modalButtons}>
            <Button
              mode="outlined"
              onPress={() => setPasswordModalVisible(false)}
              style={styles.modalButton}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleChangePassword}
              style={styles.modalButton}
            >
              Change Password
            </Button>
          </View>
        </Modal>
      </Portal>
    </ScrollView>
  );
}

const InfoItem = ({
  icon,
  label,
  value,
}: {
  icon: string;
  label: string;
  value?: string;
}) => (
  <View style={styles.infoItem}>
    <MaterialCommunityIcons
      name={icon as any}
      size={24}
      color="#6B7280"
      style={styles.infoIcon}
    />
    <View>
      <Text style={styles.infoLabel}>{label}</Text>
      <Text style={styles.infoValue}>{value || "Not set"}</Text>
    </View>
  </View>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F3F4F6",
  },
  header: {
    alignItems: "center",
    padding: 20,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E7EB",
  },
  avatarContainer: {
    position: "relative",
    marginBottom: 16,
  },
  editAvatarButton: {
    position: "absolute",
    right: 0,
    bottom: 0,
    backgroundColor: "#007AFF",
    borderRadius: 12,
    padding: 6,
  },
  name: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1F2937",
  },
  role: {
    fontSize: 16,
    color: "#6B7280",
    marginTop: 4,
  },
  section: {
    backgroundColor: "white",
    marginTop: 16,
    padding: 16,
    borderRadius: 12,
    marginHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1F2937",
  },
  infoList: {
    gap: 16,
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
  },
  infoIcon: {
    marginRight: 12,
  },
  infoLabel: {
    fontSize: 14,
    color: "#6B7280",
  },
  infoValue: {
    fontSize: 16,
    color: "#1F2937",
    marginTop: 2,
  },
  editForm: {
    gap: 12,
  },
  input: {
    backgroundColor: "white",
  },
  editButtons: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 12,
    marginTop: 16,
  },
  cancelButton: {
    minWidth: 100,
  },
  saveButton: {
    minWidth: 100,
  },
  securityButton: {
    marginTop: 8,
  },
  logoutButton: {
    margin: 16,
    marginTop: 32,
    backgroundColor: "#DC2626",
  },
  logoutButtonContent: {
    height: 48,
  },
  modal: {
    backgroundColor: "white",
    padding: 20,
    margin: 20,
    borderRadius: 12,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 16,
    color: "#1F2937",
  },
  modalButtons: {
    flexDirection: "row",
    justifyContent: "flex-end",
    gap: 12,
    marginTop: 16,
  },
  modalButton: {
    minWidth: 100,
  },
  errorText: {
    color: "#DC2626",
    fontSize: 14,
    marginTop: 8,
  },
});
