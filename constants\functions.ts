export const formatDate = (dateString: string) => {
  try {
    // Handle both string timestamp and numeric timestamp
    const date = new Date(
      isNaN(Number(dateString)) ? dateString : Number(dateString)
    );
    return date.toLocaleDateString();
  } catch (error) {
    console.error("Error formatting date:", error);
    return "Invalid date";
  }
};

export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-TZ", {
    style: "currency",
    currency: "TZS",
    maximumFractionDigits: 0,
  }).format(amount);
};
