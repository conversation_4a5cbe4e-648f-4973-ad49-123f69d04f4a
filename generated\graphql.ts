import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: any; output: any; }
};

export type Address = {
  __typename?: 'Address';
  city: Scalars['String']['output'];
  companies: Array<Company>;
  companyId: Scalars['Float']['output'];
  country: Scalars['String']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  district: Scalars['String']['output'];
  id: Scalars['Float']['output'];
  patients: Array<Patient>;
  street: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  users: Array<User>;
  ward: Scalars['String']['output'];
  zip: Scalars['String']['output'];
};

export type Approval = {
  __typename?: 'Approval';
  approvalDate?: Maybe<Scalars['DateTime']['output']>;
  approver?: Maybe<Employee>;
  approverId?: Maybe<Scalars['Float']['output']>;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  feature: Scalars['String']['output'];
  id: Scalars['Float']['output'];
  requestId: Scalars['Float']['output'];
  requester?: Maybe<Employee>;
  requesterId?: Maybe<Scalars['Float']['output']>;
  status: Scalars['Boolean']['output'];
  type: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
};

export type BatchStock = {
  __typename?: 'BatchStock';
  batch: Scalars['String']['output'];
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  expireDate: Scalars['String']['output'];
  id: Scalars['Float']['output'];
  item: Item;
  itemId: Scalars['Float']['output'];
  stock: Scalars['Float']['output'];
  storeItemStocks?: Maybe<Array<StoreItemStock>>;
  updatedAt: Scalars['String']['output'];
};

export type Bill = {
  __typename?: 'Bill';
  amount: Scalars['Float']['output'];
  cleared: Scalars['Boolean']['output'];
  client?: Maybe<Patient>;
  clientId?: Maybe<Scalars['Float']['output']>;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  id: Scalars['Float']['output'];
  inventoryId: Scalars['Float']['output'];
  inventoryTransfer: Inventory;
  paymentType: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  visit?: Maybe<Visit>;
  visitId?: Maybe<Scalars['Float']['output']>;
};

/** Billing cycle for payment */
export enum BillingCycle {
  Annually = 'ANNUALLY',
  Monthly = 'MONTHLY',
  Quarterly = 'QUARTERLY'
}

export type BooleanResponse = {
  __typename?: 'BooleanResponse';
  error?: Maybe<FieldError>;
  status: Scalars['Boolean']['output'];
};

export type BooleanResponseId = {
  __typename?: 'BooleanResponseId';
  error?: Maybe<FieldError>;
  id?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  status: Scalars['Boolean']['output'];
};

export type BooleanResponseWithType = {
  __typename?: 'BooleanResponseWithType';
  data?: Maybe<Type>;
  error?: Maybe<FieldError>;
  status: Scalars['Boolean']['output'];
};

export type BulkItemInput = {
  items: Array<ItemInput>;
};

export type BulkScheduleResponse = {
  __typename?: 'BulkScheduleResponse';
  error?: Maybe<FieldError>;
  schedules?: Maybe<Array<Schedule>>;
  status: Scalars['Boolean']['output'];
};

export type Category = {
  __typename?: 'Category';
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  id: Scalars['Float']['output'];
  name: Scalars['String']['output'];
  parentCategoryId?: Maybe<Scalars['Float']['output']>;
  type: Type;
  typeId: Scalars['Float']['output'];
  updatedAt: Scalars['String']['output'];
  user?: Maybe<Array<User>>;
};

export type CategoryArgs = {
  name: Scalars['String']['input'];
  type: Scalars['Float']['input'];
};

export type CategoryResponse = {
  __typename?: 'CategoryResponse';
  category?: Maybe<Category>;
  error?: Maybe<FieldError>;
};

export type CategoryTypeArgs = {
  name: Scalars['String']['input'];
  typeName: Scalars['String']['input'];
};

export type Clinic = {
  __typename?: 'Clinic';
  clinicType: Scalars['String']['output'];
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  department?: Maybe<Department>;
  departmentId: Scalars['Float']['output'];
  description: Scalars['String']['output'];
  id: Scalars['Float']['output'];
  leader?: Maybe<Employee>;
  leaderId?: Maybe<Scalars['Float']['output']>;
  name: Scalars['String']['output'];
  schedules?: Maybe<Array<Schedule>>;
  size: Scalars['Float']['output'];
  status: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  visitsToClinic?: Maybe<Array<VisitToClinic>>;
};

export type ClinicEditArgs = {
  clinicType: Scalars['String']['input'];
  description: Scalars['String']['input'];
  leaderId?: InputMaybe<Scalars['Int']['input']>;
  name: Scalars['String']['input'];
  size: Scalars['Int']['input'];
  status: Scalars['String']['input'];
};

export type ClinicInputArgs = {
  clinicType: Scalars['String']['input'];
  departmentId: Scalars['Int']['input'];
  description: Scalars['String']['input'];
  leaderId?: InputMaybe<Scalars['Int']['input']>;
  name: Scalars['String']['input'];
  size: Scalars['Int']['input'];
  status: Scalars['String']['input'];
};

export type ClinicResponse = {
  __typename?: 'ClinicResponse';
  clinic?: Maybe<Clinic>;
  error?: Maybe<FieldError>;
  status: Scalars['Boolean']['output'];
};

export type Company = {
  __typename?: 'Company';
  branches: Array<Scalars['Float']['output']>;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  departments: Array<Department>;
  email: Scalars['String']['output'];
  employees: Array<Employee>;
  features?: Maybe<Array<Feature>>;
  id: Scalars['Float']['output'];
  isBranch: Scalars['Boolean']['output'];
  isParent: Scalars['Boolean']['output'];
  location: Scalars['String']['output'];
  logo: Scalars['String']['output'];
  name: Scalars['String']['output'];
  parentId: Scalars['Float']['output'];
  payments?: Maybe<Array<Payment>>;
  phone: Scalars['String']['output'];
  poBox: Scalars['String']['output'];
  registrationNumber: Scalars['String']['output'];
  syncHistory?: Maybe<Array<SyncHistory>>;
  syncUrl?: Maybe<Scalars['String']['output']>;
  tinNumber: Scalars['String']['output'];
  type: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  users?: Maybe<Array<User>>;
  website: Scalars['String']['output'];
};

export type CreatePaymentInput = {
  amount: Scalars['Float']['input'];
  autoRenew?: InputMaybe<Scalars['Boolean']['input']>;
  billingCycle: Scalars['String']['input'];
  endDate: Scalars['DateTime']['input'];
  features?: InputMaybe<Scalars['String']['input']>;
  maxUsers?: InputMaybe<Scalars['Float']['input']>;
  packageName: Scalars['String']['input'];
  paymentReference?: InputMaybe<Scalars['String']['input']>;
  startDate: Scalars['DateTime']['input'];
  status: Scalars['String']['input'];
};

/** The days of the week */
export enum DayOfWeek {
  Friday = 'FRIDAY',
  Monday = 'MONDAY',
  Saturday = 'SATURDAY',
  Sunday = 'SUNDAY',
  Thursday = 'THURSDAY',
  Tuesday = 'TUESDAY',
  Wednesday = 'WEDNESDAY'
}

export type Department = {
  __typename?: 'Department';
  clinics?: Maybe<Array<Clinic>>;
  company: Company;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  description: Scalars['String']['output'];
  employees?: Maybe<Array<Employee>>;
  headOfDepartment?: Maybe<Employee>;
  id: Scalars['Float']['output'];
  name: Scalars['String']['output'];
  parentId?: Maybe<Scalars['Float']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  updatedAt: Scalars['String']['output'];
};

export type DepartmentInputArgs = {
  description?: InputMaybe<Scalars['String']['input']>;
  headOfDepartmentId?: InputMaybe<Scalars['Float']['input']>;
  name: Scalars['String']['input'];
  parentId?: InputMaybe<Scalars['Float']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type DispatchInput = {
  batch?: InputMaybe<Scalars['String']['input']>;
  itemId: Scalars['Float']['input'];
  locationId: Scalars['Float']['input'];
  quantity: Scalars['Float']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
  unit: Scalars['String']['input'];
};

export type EditUserArgs = {
  email: Scalars['String']['input'];
  firstname: Scalars['String']['input'];
  image: Scalars['String']['input'];
  lastname: Scalars['String']['input'];
  middlename: Scalars['String']['input'];
  phone: Scalars['String']['input'];
};

export type EmailPasswordArgs = {
  email: Scalars['String']['input'];
  password: Scalars['String']['input'];
};

export type Employee = {
  __typename?: 'Employee';
  approvedApprovals?: Maybe<Array<Approval>>;
  approved_stock?: Maybe<Array<Inventory>>;
  attended?: Maybe<Array<VisitToClinic>>;
  authorizedExpenses?: Maybe<Array<Expense>>;
  company: Company;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  department?: Maybe<Department>;
  departmentId?: Maybe<Scalars['Float']['output']>;
  designation: Scalars['String']['output'];
  headingDepartment?: Maybe<Department>;
  headingDepartmentId?: Maybe<Scalars['Float']['output']>;
  id: Scalars['Float']['output'];
  image: Scalars['String']['output'];
  leadClinic?: Maybe<Clinic>;
  licenceNumber: Scalars['String']['output'];
  received_stock?: Maybe<Array<Inventory>>;
  registeredPatients?: Maybe<Array<Patient>>;
  requestedApprovals?: Maybe<Array<Approval>>;
  requestedExpenses?: Maybe<Array<Expense>>;
  role: Role;
  roleId: Scalars['Float']['output'];
  schedules?: Maybe<Array<Schedule>>;
  served_stock?: Maybe<Array<Inventory>>;
  status: Scalars['String']['output'];
  store?: Maybe<Store>;
  storeId?: Maybe<Scalars['Float']['output']>;
  updatedAt: Scalars['String']['output'];
  user: User;
  userId: Scalars['Float']['output'];
};

export type Expense = {
  __typename?: 'Expense';
  amount: Scalars['Float']['output'];
  assetId: Scalars['Float']['output'];
  assetType: Scalars['String']['output'];
  authorizer?: Maybe<Employee>;
  authorizerId: Scalars['Float']['output'];
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  details: Scalars['String']['output'];
  expenseDate: Scalars['String']['output'];
  id: Scalars['Float']['output'];
  requester?: Maybe<Employee>;
  requesterId: Scalars['Float']['output'];
  status: Scalars['String']['output'];
  title: Scalars['String']['output'];
  type: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
};

export type ExpenseFilterInput = {
  endDate?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['String']['input']>;
};

export type ExpenseInput = {
  amount: Scalars['Float']['input'];
  assetId?: InputMaybe<Scalars['Float']['input']>;
  assetType?: InputMaybe<Scalars['String']['input']>;
  details: Scalars['String']['input'];
  expenseDate: Scalars['String']['input'];
  title: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type Feature = {
  __typename?: 'Feature';
  companies?: Maybe<Array<Company>>;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  id: Scalars['Float']['output'];
  name: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
};

export type FieldError = {
  __typename?: 'FieldError';
  message: Scalars['String']['output'];
  target: Scalars['String']['output'];
};

export type Import = {
  __typename?: 'Import';
  batch: Scalars['String']['output'];
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  expireDate: Scalars['String']['output'];
  id: Scalars['Float']['output'];
  importDate: Scalars['String']['output'];
  importPrice: Scalars['Float']['output'];
  item: Item;
  itemId: Scalars['Float']['output'];
  quantity: Scalars['Float']['output'];
  receipt: Scalars['String']['output'];
  sellingPrice: Scalars['Float']['output'];
  supplier: Scalars['String']['output'];
  unit: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
};

export type ImportInput = {
  batch?: InputMaybe<Scalars['String']['input']>;
  expireDate?: InputMaybe<Scalars['String']['input']>;
  importDate: Scalars['String']['input'];
  importPrice: Scalars['Float']['input'];
  itemId: Scalars['Float']['input'];
  pieceSellingPrice?: InputMaybe<Scalars['Float']['input']>;
  quantity: Scalars['Float']['input'];
  receipt?: InputMaybe<Scalars['String']['input']>;
  sellingPrice?: InputMaybe<Scalars['Float']['input']>;
  subPieceSellingPrice?: InputMaybe<Scalars['Float']['input']>;
  supplier: Scalars['String']['input'];
  unit: Scalars['String']['input'];
};

export type Inventory = {
  __typename?: 'Inventory';
  approver?: Maybe<Employee>;
  approverId?: Maybe<Scalars['Float']['output']>;
  bill?: Maybe<Bill>;
  companyId: Scalars['Float']['output'];
  consumer?: Maybe<Employee>;
  consumerId?: Maybe<Scalars['Float']['output']>;
  createdAt: Scalars['String']['output'];
  customerTag?: Maybe<Scalars['String']['output']>;
  deleted: Scalars['Boolean']['output'];
  destinationStore?: Maybe<Store>;
  destinationStoreId?: Maybe<Scalars['Float']['output']>;
  details?: Maybe<Scalars['String']['output']>;
  dispatched: Scalars['Boolean']['output'];
  granted: Scalars['Boolean']['output'];
  id: Scalars['Float']['output'];
  items: Array<Item>;
  keeper?: Maybe<Employee>;
  keeperId?: Maybe<Scalars['Float']['output']>;
  received: Scalars['Boolean']['output'];
  returnDate?: Maybe<Scalars['String']['output']>;
  sourceStore?: Maybe<Store>;
  sourceStoreId?: Maybe<Scalars['Float']['output']>;
  startDate?: Maybe<Scalars['String']['output']>;
  transferDate: Scalars['String']['output'];
  transfers: Array<Transfer>;
  type: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
};

export type Item = {
  __typename?: 'Item';
  barcode?: Maybe<Scalars['String']['output']>;
  batchStocks?: Maybe<Array<BatchStock>>;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  description: Scalars['String']['output'];
  id: Scalars['Float']['output'];
  image: Scalars['String']['output'];
  imports?: Maybe<Array<Import>>;
  internal: Scalars['Boolean']['output'];
  inventoryTransfers?: Maybe<Array<Inventory>>;
  name: Scalars['String']['output'];
  reference: Scalars['String']['output'];
  reorder: Scalars['Float']['output'];
  sellingPrice: Scalars['Float']['output'];
  stock: Scalars['Float']['output'];
  storeItemStocks?: Maybe<Array<StoreItemStock>>;
  transfers?: Maybe<Array<Transfer>>;
  type: Scalars['String']['output'];
  unit: Scalars['String']['output'];
  units: Array<Unit>;
  updatedAt: Scalars['String']['output'];
};

export type ItemInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  image?: InputMaybe<Scalars['String']['input']>;
  internal?: InputMaybe<Scalars['Boolean']['input']>;
  name: Scalars['String']['input'];
  reference?: InputMaybe<Scalars['String']['input']>;
  reorder: Scalars['Float']['input'];
  sellingPrice?: InputMaybe<Scalars['Float']['input']>;
  type: Scalars['String']['input'];
  unit: Scalars['String']['input'];
};

export type LogEntry = {
  __typename?: 'LogEntry';
  action?: Maybe<Scalars['String']['output']>;
  companyId?: Maybe<Scalars['Int']['output']>;
  errorCode?: Maybe<Scalars['String']['output']>;
  level: Scalars['String']['output'];
  message: Scalars['String']['output'];
  severity?: Maybe<Scalars['String']['output']>;
  stackTrace?: Maybe<Scalars['String']['output']>;
  timestamp: Scalars['String']['output'];
  userId?: Maybe<Scalars['Int']['output']>;
};

export type Message = {
  __typename?: 'Message';
  attended: Scalars['Boolean']['output'];
  createdAt: Scalars['String']['output'];
  id: Scalars['Float']['output'];
  message: Scalars['String']['output'];
  senderEmail: Scalars['String']['output'];
  senderName: Scalars['String']['output'];
  senderPhone?: Maybe<Scalars['String']['output']>;
  subject: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
};

export type MessageInput = {
  message: Scalars['String']['input'];
  senderEmail: Scalars['String']['input'];
  senderName: Scalars['String']['input'];
  senderPhone?: InputMaybe<Scalars['String']['input']>;
  subject: Scalars['String']['input'];
};

export type MessageResponse = {
  __typename?: 'MessageResponse';
  errors?: Maybe<Array<FieldError>>;
  message?: Maybe<Message>;
  status: Scalars['Boolean']['output'];
};

export type Mutation = {
  __typename?: 'Mutation';
  addCategory: BooleanResponse;
  addCategoryWithTypeName: CategoryResponse;
  addClinic: ClinicResponse;
  addCompanyWithAddress: BooleanResponse;
  addDepartment: BooleanResponse;
  addExpense: BooleanResponse;
  addFeature: BooleanResponse;
  addItem: BooleanResponse;
  addItemsFromExcel: BooleanResponse;
  addPermission: PermissionResponse;
  addRole: BooleanResponse;
  addSchedules: BulkScheduleResponse;
  addService: BooleanResponse;
  addStore: BooleanResponse;
  addType: BooleanResponseWithType;
  addUnit: BooleanResponse;
  addVisit: VisitResponse;
  addVitals: VisitResponse;
  assignStoreKeeper: BooleanResponse;
  authorizeExpense: BooleanResponse;
  cancelPayment: BooleanResponse;
  cancelSchedule: BooleanResponse;
  changeEmployeeRole: BooleanResponse;
  changeEmployeeStatus: BooleanResponse;
  changeInventoryApprovalStatus: BooleanResponse;
  changeInventoryDispatchedStatus: BooleanResponse;
  changeInventoryReceivedStatus: BooleanResponse;
  changeInventorySoldStatus: BooleanResponse;
  changePassword: BooleanResponse;
  changePaymentStatus: BooleanResponse;
  clearBill: BooleanResponse;
  clearServedBill: BooleanResponse;
  createPayment: Payment;
  deleteBillItem: BooleanResponse;
  deleteCategory: BooleanResponse;
  deleteClinic: BooleanResponse;
  deleteExpense: BooleanResponse;
  deleteFeature: BooleanResponse;
  deleteItem: BooleanResponse;
  deletePayment: BooleanResponse;
  deletePermission: BooleanResponse;
  deleteRole: BooleanResponse;
  deleteSchedule: BooleanResponse;
  deleteType: BooleanResponse;
  deleteUnit: BooleanResponse;
  dispatchItems: BooleanResponse;
  editBillItem: TransferResponse;
  editCategory: BooleanResponse;
  editCategoryByName: BooleanResponse;
  editClinic: ClinicResponse;
  editDepartment: BooleanResponse;
  editExpense: BooleanResponse;
  editFeature: BooleanResponse;
  editItem: BooleanResponse;
  editPatient: PatientResponse;
  editPermission: BooleanResponse;
  editRole: BooleanResponse;
  editSchedules: BulkScheduleResponse;
  editService: BooleanResponse;
  editStore: BooleanResponse;
  editType: BooleanResponse;
  editUser: BooleanResponse;
  forgotPassword: BooleanResponse;
  importItem: BooleanResponse;
  instantTransfer: BooleanResponse;
  login: UserResponse;
  logout: Scalars['Boolean']['output'];
  manageUserPermissions: BooleanResponse;
  quickSale: BooleanResponse;
  reassignClinicDoctor: BooleanResponse;
  receiveMessage: MessageResponse;
  register: BooleanResponse;
  registerCompany: BooleanResponseId;
  registerEmployee: BooleanResponse;
  registerPatient: PatientResponse;
  removePermission: BooleanResponse;
  resetPassword: UserResponse;
  servePayLater: BooleanResponse;
  servePendingOrder: BooleanResponse;
  setHeadOfDepartment: BooleanResponse;
  transferItems: BooleanResponse;
  triggerSync: BooleanResponse;
  updateBill: BooleanResponse;
  updateMessageStatus: MessageResponse;
  updatePayment?: Maybe<Payment>;
  updateScheduleStatus: BooleanResponse;
  updateSyncConfig: BooleanResponse;
  updateUnit: BooleanResponse;
  writeOffItems: BooleanResponse;
};


export type MutationAddCategoryArgs = {
  args: CategoryArgs;
};


export type MutationAddCategoryWithTypeNameArgs = {
  args: CategoryTypeArgs;
};


export type MutationAddClinicArgs = {
  params: ClinicInputArgs;
};


export type MutationAddCompanyWithAddressArgs = {
  params: RegisterCompanyAddressedArgs;
};


export type MutationAddDepartmentArgs = {
  params: DepartmentInputArgs;
};


export type MutationAddExpenseArgs = {
  args: ExpenseInput;
};


export type MutationAddFeatureArgs = {
  companyId: Scalars['Float']['input'];
  name: Scalars['String']['input'];
};


export type MutationAddItemArgs = {
  args: ItemInput;
};


export type MutationAddItemsFromExcelArgs = {
  args: BulkItemInput;
};


export type MutationAddPermissionArgs = {
  name: Scalars['String']['input'];
  roleId?: InputMaybe<Scalars['Float']['input']>;
  userId?: InputMaybe<Scalars['Float']['input']>;
};


export type MutationAddRoleArgs = {
  name: Scalars['String']['input'];
};


export type MutationAddSchedulesArgs = {
  args: ScheduleBulkArgs;
};


export type MutationAddServiceArgs = {
  args: ServiceInput;
};


export type MutationAddStoreArgs = {
  args: StoreInput;
};


export type MutationAddTypeArgs = {
  args: TypeArgs;
};


export type MutationAddUnitArgs = {
  args: UnitInput;
};


export type MutationAddVisitArgs = {
  params: VisitInputArgs;
};


export type MutationAddVitalsArgs = {
  params: VitalsInputArgs;
};


export type MutationAssignStoreKeeperArgs = {
  storeId: Scalars['Float']['input'];
  userId: Scalars['Float']['input'];
};


export type MutationAuthorizeExpenseArgs = {
  id: Scalars['Float']['input'];
};


export type MutationCancelPaymentArgs = {
  id: Scalars['Float']['input'];
  reason?: InputMaybe<Scalars['String']['input']>;
};


export type MutationCancelScheduleArgs = {
  reason?: InputMaybe<Scalars['String']['input']>;
  scheduleId: Scalars['Float']['input'];
};


export type MutationChangeEmployeeRoleArgs = {
  companyRole: Scalars['Float']['input'];
  departmentId: Scalars['Float']['input'];
  designation: Scalars['String']['input'];
  employeeId: Scalars['Float']['input'];
};


export type MutationChangeEmployeeStatusArgs = {
  employeeId: Scalars['Float']['input'];
  status: Scalars['String']['input'];
};


export type MutationChangeInventoryApprovalStatusArgs = {
  inventoryId: Scalars['Float']['input'];
};


export type MutationChangeInventoryDispatchedStatusArgs = {
  inventoryId: Scalars['Float']['input'];
};


export type MutationChangeInventoryReceivedStatusArgs = {
  inventoryId: Scalars['Float']['input'];
};


export type MutationChangeInventorySoldStatusArgs = {
  inventoryId: Scalars['Float']['input'];
};


export type MutationChangePasswordArgs = {
  currentPassword: Scalars['String']['input'];
  newPassword: Scalars['String']['input'];
};


export type MutationChangePaymentStatusArgs = {
  id: Scalars['Float']['input'];
  status: Scalars['String']['input'];
};


export type MutationClearBillArgs = {
  saleId: Scalars['Float']['input'];
};


export type MutationClearServedBillArgs = {
  inventoryId: Scalars['Float']['input'];
};


export type MutationCreatePaymentArgs = {
  input: CreatePaymentInput;
};


export type MutationDeleteBillItemArgs = {
  inventoryId: Scalars['Float']['input'];
  transferId: Scalars['Float']['input'];
};


export type MutationDeleteCategoryArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDeleteClinicArgs = {
  id: Scalars['Int']['input'];
};


export type MutationDeleteExpenseArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDeleteFeatureArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDeleteItemArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDeletePaymentArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDeletePermissionArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDeleteRoleArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDeleteScheduleArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDeleteTypeArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDeleteUnitArgs = {
  id: Scalars['Float']['input'];
};


export type MutationDispatchItemsArgs = {
  args: Array<DispatchInput>;
};


export type MutationEditBillItemArgs = {
  inventoryId: Scalars['Float']['input'];
  newQuantity: Scalars['Float']['input'];
  transferId: Scalars['Float']['input'];
};


export type MutationEditCategoryArgs = {
  args: CategoryArgs;
  id: Scalars['Float']['input'];
};


export type MutationEditCategoryByNameArgs = {
  args: CategoryArgs;
  name: Scalars['String']['input'];
};


export type MutationEditClinicArgs = {
  id: Scalars['Int']['input'];
  params: ClinicEditArgs;
};


export type MutationEditDepartmentArgs = {
  id: Scalars['Float']['input'];
  params: DepartmentInputArgs;
};


export type MutationEditExpenseArgs = {
  args: ExpenseInput;
  id: Scalars['Float']['input'];
};


export type MutationEditFeatureArgs = {
  id: Scalars['Float']['input'];
  name: Scalars['String']['input'];
};


export type MutationEditItemArgs = {
  args: ItemInput;
  id: Scalars['Float']['input'];
};


export type MutationEditPatientArgs = {
  id: Scalars['Float']['input'];
  params: RegisterPatientArgs;
};


export type MutationEditPermissionArgs = {
  id: Scalars['Float']['input'];
  name: Scalars['String']['input'];
};


export type MutationEditRoleArgs = {
  args: RoleArgs;
  id: Scalars['Float']['input'];
};


export type MutationEditSchedulesArgs = {
  args: ScheduleEditBulkArgs;
};


export type MutationEditServiceArgs = {
  args: ServiceInput;
  id: Scalars['Float']['input'];
};


export type MutationEditStoreArgs = {
  args: StoreEditInput;
};


export type MutationEditTypeArgs = {
  args: TypeEditArgs;
  id: Scalars['Float']['input'];
};


export type MutationEditUserArgs = {
  id: Scalars['Float']['input'];
  params: EditUserArgs;
};


export type MutationForgotPasswordArgs = {
  email: Scalars['String']['input'];
};


export type MutationImportItemArgs = {
  args: ImportInput;
};


export type MutationInstantTransferArgs = {
  args: Array<TransferInput>;
  destinationStore: Scalars['Float']['input'];
  sourceStore: Scalars['Float']['input'];
};


export type MutationLoginArgs = {
  params: EmailPasswordArgs;
};


export type MutationManageUserPermissionsArgs = {
  id: Scalars['Float']['input'];
  permissions: Array<Scalars['Float']['input']>;
};


export type MutationQuickSaleArgs = {
  args: Array<SaleInput>;
};


export type MutationReassignClinicDoctorArgs = {
  clinicId: Scalars['Int']['input'];
  newDoctorId: Scalars['Int']['input'];
};


export type MutationReceiveMessageArgs = {
  input: MessageInput;
};


export type MutationRegisterArgs = {
  params: RegisterUserArgs;
};


export type MutationRegisterCompanyArgs = {
  params: RegisterCompanyArgs;
};


export type MutationRegisterEmployeeArgs = {
  params: RegisterEmployeeArgs;
};


export type MutationRegisterPatientArgs = {
  params: RegisterPatientArgs;
};


export type MutationRemovePermissionArgs = {
  name: Scalars['String']['input'];
  roleId?: InputMaybe<Scalars['Float']['input']>;
  userId?: InputMaybe<Scalars['Float']['input']>;
};


export type MutationResetPasswordArgs = {
  newPassword: Scalars['String']['input'];
  token: Scalars['String']['input'];
};


export type MutationServePayLaterArgs = {
  args: Array<SaleInput>;
  customerTag?: InputMaybe<Scalars['String']['input']>;
  servedTo?: InputMaybe<Scalars['Float']['input']>;
};


export type MutationServePendingOrderArgs = {
  transferId: Scalars['Float']['input'];
};


export type MutationSetHeadOfDepartmentArgs = {
  departmentId: Scalars['Int']['input'];
  employeeId: Scalars['Int']['input'];
};


export type MutationTransferItemsArgs = {
  args: Array<DispatchInput>;
};


export type MutationTriggerSyncArgs = {
  companyId: Scalars['Float']['input'];
};


export type MutationUpdateBillArgs = {
  args: Array<SaleInput>;
  inventoryId: Scalars['Float']['input'];
};


export type MutationUpdateMessageStatusArgs = {
  attended: Scalars['Boolean']['input'];
  messageId: Scalars['Float']['input'];
};


export type MutationUpdatePaymentArgs = {
  id: Scalars['Float']['input'];
  input: UpdatePaymentInput;
};


export type MutationUpdateScheduleStatusArgs = {
  scheduleId: Scalars['Float']['input'];
  status: Scalars['String']['input'];
};


export type MutationUpdateSyncConfigArgs = {
  config: SyncConfigInput;
};


export type MutationUpdateUnitArgs = {
  args: UnitInput;
  id: Scalars['Float']['input'];
};


export type MutationWriteOffItemsArgs = {
  args: Array<WriteOffInput>;
};

export type Patient = {
  __typename?: 'Patient';
  address: Address;
  addressId: Scalars['Float']['output'];
  alergies?: Maybe<Array<Scalars['String']['output']>>;
  bills: Array<Bill>;
  bloodGroup?: Maybe<Scalars['String']['output']>;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  dateOfBirth: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  email?: Maybe<Scalars['String']['output']>;
  fileNumber?: Maybe<Scalars['String']['output']>;
  firstname: Scalars['String']['output'];
  gender: Scalars['String']['output'];
  id: Scalars['Float']['output'];
  image?: Maybe<Scalars['String']['output']>;
  insuranceCardNumber?: Maybe<Scalars['String']['output']>;
  insuranceId?: Maybe<Scalars['String']['output']>;
  insuranceProvider: Scalars['String']['output'];
  insuranceSchemeId?: Maybe<Scalars['String']['output']>;
  insuranceStatus: Scalars['String']['output'];
  insuranceUserId: Scalars['String']['output'];
  lastname: Scalars['String']['output'];
  middlename: Scalars['String']['output'];
  nationalId?: Maybe<Scalars['String']['output']>;
  nextOfKinName: Scalars['String']['output'];
  nextOfKinPhone: Scalars['String']['output'];
  nextOfKinRelationship: Scalars['String']['output'];
  otherId?: Maybe<Scalars['String']['output']>;
  phone: Scalars['String']['output'];
  registerer: Employee;
  registererId?: Maybe<Scalars['Float']['output']>;
  religion: Scalars['String']['output'];
  status: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  visits: Array<Visit>;
};

export type PatientResponse = {
  __typename?: 'PatientResponse';
  error?: Maybe<FieldError>;
  patient?: Maybe<Patient>;
  status: Scalars['Boolean']['output'];
};

export type Payment = {
  __typename?: 'Payment';
  amount: Scalars['Float']['output'];
  autoRenew: Scalars['Boolean']['output'];
  billingCycle: BillingCycle;
  cancellationReason?: Maybe<Scalars['String']['output']>;
  company: Company;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  endDate: Scalars['String']['output'];
  features?: Maybe<Scalars['String']['output']>;
  id: Scalars['Float']['output'];
  lastPaymentDate?: Maybe<Scalars['String']['output']>;
  maxUsers?: Maybe<Scalars['Float']['output']>;
  packageName: Scalars['String']['output'];
  paymentReference?: Maybe<Scalars['String']['output']>;
  startDate: Scalars['String']['output'];
  status: PaymentStatus;
  updatedAt: Scalars['String']['output'];
};

/** Status of a payment */
export enum PaymentStatus {
  Active = 'ACTIVE',
  Canceled = 'CANCELED',
  Expired = 'EXPIRED',
  Pending = 'PENDING',
  Trial = 'TRIAL'
}

export type Permission = {
  __typename?: 'Permission';
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  id: Scalars['Float']['output'];
  name: Scalars['String']['output'];
  roles?: Maybe<Array<Role>>;
  updatedAt: Scalars['String']['output'];
  users?: Maybe<Array<User>>;
};

export type PermissionResponse = {
  __typename?: 'PermissionResponse';
  error?: Maybe<FieldError>;
  permission?: Maybe<Permission>;
  status: Scalars['Boolean']['output'];
};

export type Query = {
  __typename?: 'Query';
  expenses: Array<Expense>;
  getActivePayment?: Maybe<Payment>;
  getAllCategories: Array<Category>;
  getAllItems: Array<Item>;
  getAllMessages: Array<Message>;
  getAllServices: Array<Item>;
  getBatchStockForStore: Array<BatchStock>;
  getCategories: Array<Category>;
  getClinic?: Maybe<Clinic>;
  getClinics: Array<Clinic>;
  getCompanies: Array<Company>;
  getCompany?: Maybe<Company>;
  getCompanyErrors: Array<LogEntry>;
  getDepartments: Array<Department>;
  getDispatches: Array<Inventory>;
  getEmployees: Array<User>;
  getExpense?: Maybe<Expense>;
  getExpenses: Array<Expense>;
  getFeatures: Array<Feature>;
  getInternalItems: Array<Item>;
  getInventoryTransfer?: Maybe<Inventory>;
  getInventoryTransfers: Array<Inventory>;
  getItem: Item;
  getItemBatchImports: Array<Import>;
  getItemBatchStocks: Array<BatchStock>;
  getItemStoreStocks: Array<StoreItemStock>;
  getItemTransfers: Array<Transfer>;
  getItemUnits: Array<Unit>;
  getItems: Array<Item>;
  getLatestPayment?: Maybe<Payment>;
  getLogs: Array<LogEntry>;
  getMerchandiseItems: Array<Item>;
  getMessages: Array<Message>;
  getOpenTabs: Array<Inventory>;
  getPatient?: Maybe<Patient>;
  getPatients: Array<Patient>;
  getPayment?: Maybe<Payment>;
  getPermissions: Array<Permission>;
  getRole?: Maybe<Role>;
  getRoles: Array<Role>;
  getSales: Array<Inventory>;
  getSalesPOS: Array<Inventory>;
  getSchedules: Array<Schedule>;
  getStoreItems: Array<Item>;
  getStores: Array<Store>;
  getSyncConfig: SyncConfig;
  getTodaysErrors: Array<LogEntry>;
  getTransfers: Array<Inventory>;
  getType?: Maybe<Type>;
  getTypes: Array<Type>;
  getUndetailedEmployees: Array<UndetailedUser>;
  getUnit?: Maybe<Unit>;
  getUser?: Maybe<User>;
  getUsers: Array<User>;
  getVisits: Array<Visit>;
  getWriteOffsByCompany: Array<Transfer>;
  lastSyncStatus?: Maybe<SyncHistory>;
  me?: Maybe<User>;
  payments: Array<Payment>;
  schedules: Array<Schedule>;
  syncHistory: Array<SyncHistory>;
};


export type QueryExpensesArgs = {
  filter?: InputMaybe<ExpenseFilterInput>;
};


export type QueryGetActivePaymentArgs = {
  companyId: Scalars['Float']['input'];
};


export type QueryGetAllMessagesArgs = {
  endDate: Scalars['DateTime']['input'];
  startDate: Scalars['DateTime']['input'];
};


export type QueryGetBatchStockForStoreArgs = {
  itemId?: InputMaybe<Scalars['Float']['input']>;
  storeId?: InputMaybe<Scalars['Float']['input']>;
};


export type QueryGetCategoriesArgs = {
  type: Scalars['String']['input'];
};


export type QueryGetClinicArgs = {
  id: Scalars['Int']['input'];
};


export type QueryGetCompanyArgs = {
  id: Scalars['Float']['input'];
};


export type QueryGetCompanyErrorsArgs = {
  companyId: Scalars['Int']['input'];
  days?: InputMaybe<Scalars['Int']['input']>;
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  severity?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
};


export type QueryGetExpenseArgs = {
  id: Scalars['Float']['input'];
};


export type QueryGetInventoryTransferArgs = {
  id: Scalars['Float']['input'];
};


export type QueryGetInventoryTransfersArgs = {
  type?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetItemArgs = {
  id: Scalars['Float']['input'];
};


export type QueryGetItemBatchImportsArgs = {
  itemId: Scalars['Float']['input'];
};


export type QueryGetItemBatchStocksArgs = {
  itemId: Scalars['Float']['input'];
};


export type QueryGetItemStoreStocksArgs = {
  itemId: Scalars['Float']['input'];
};


export type QueryGetItemTransfersArgs = {
  itemId: Scalars['Float']['input'];
  type?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetItemUnitsArgs = {
  itemId: Scalars['Float']['input'];
};


export type QueryGetLogsArgs = {
  companyId?: InputMaybe<Scalars['Int']['input']>;
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  errorCode?: InputMaybe<Scalars['String']['input']>;
  level?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
  userId?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryGetMessagesArgs = {
  attended?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryGetPatientArgs = {
  id: Scalars['Float']['input'];
};


export type QueryGetPaymentArgs = {
  id: Scalars['Float']['input'];
};


export type QueryGetRoleArgs = {
  name: Scalars['String']['input'];
};


export type QueryGetRolesArgs = {
  sys?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryGetSalesArgs = {
  date?: InputMaybe<Scalars['DateTime']['input']>;
};


export type QueryGetSchedulesArgs = {
  owner: Scalars['String']['input'];
  ownerId: Scalars['Int']['input'];
};


export type QueryGetStoreItemsArgs = {
  storeId: Scalars['Float']['input'];
};


export type QueryGetTypeArgs = {
  id: Scalars['Float']['input'];
};


export type QueryGetUndetailedEmployeesArgs = {
  companyId: Scalars['Float']['input'];
};


export type QueryGetUnitArgs = {
  id: Scalars['Float']['input'];
};


export type QueryGetUserArgs = {
  id: Scalars['Float']['input'];
};


export type QueryGetUsersArgs = {
  roles?: InputMaybe<Array<Scalars['Float']['input']>>;
};


export type QueryLastSyncStatusArgs = {
  entityName: Scalars['String']['input'];
};


export type QuerySchedulesArgs = {
  clinicId?: InputMaybe<Scalars['Float']['input']>;
  endDate: Scalars['DateTime']['input'];
  startDate: Scalars['DateTime']['input'];
};


export type QuerySyncHistoryArgs = {
  companyId: Scalars['Float']['input'];
  entityName?: InputMaybe<Scalars['String']['input']>;
  limit?: InputMaybe<Scalars['Float']['input']>;
};

export type RegisterCompanyAddressedArgs = {
  city: Scalars['String']['input'];
  district: Scalars['String']['input'];
  name: Scalars['String']['input'];
  registrationNumber: Scalars['String']['input'];
  street: Scalars['String']['input'];
  tinNumber: Scalars['String']['input'];
  type: Scalars['String']['input'];
  ward: Scalars['String']['input'];
};

export type RegisterCompanyArgs = {
  location?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  registrationNumber: Scalars['String']['input'];
  tinNumber: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type RegisterEmployeeArgs = {
  companyRole?: InputMaybe<Scalars['Float']['input']>;
  department?: InputMaybe<Scalars['Float']['input']>;
  designation: Scalars['String']['input'];
  email: Scalars['String']['input'];
  firstname: Scalars['String']['input'];
  lastname: Scalars['String']['input'];
  licenseNumber: Scalars['String']['input'];
  middlename: Scalars['String']['input'];
  password: Scalars['String']['input'];
  phone: Scalars['String']['input'];
  store?: InputMaybe<Scalars['Float']['input']>;
};

export type RegisterPatientArgs = {
  DOB: Scalars['String']['input'];
  city: Scalars['String']['input'];
  country: Scalars['String']['input'];
  district: Scalars['String']['input'];
  email: Scalars['String']['input'];
  firstname: Scalars['String']['input'];
  gender: Scalars['String']['input'];
  insuranceProvider: Scalars['String']['input'];
  insuranceStatus: Scalars['String']['input'];
  insuranceUserId: Scalars['String']['input'];
  lastname: Scalars['String']['input'];
  middlename: Scalars['String']['input'];
  nationalId: Scalars['String']['input'];
  nextOfKinName: Scalars['String']['input'];
  nextOfKinPhone: Scalars['String']['input'];
  nextOfKinRelationship: Scalars['String']['input'];
  phone: Scalars['String']['input'];
  religion: Scalars['String']['input'];
  status: Scalars['String']['input'];
  street: Scalars['String']['input'];
  ward: Scalars['String']['input'];
};

export type RegisterUserArgs = {
  companyId: Scalars['Float']['input'];
  email: Scalars['String']['input'];
  firstname: Scalars['String']['input'];
  lastname: Scalars['String']['input'];
  middlename: Scalars['String']['input'];
  password: Scalars['String']['input'];
  phone: Scalars['String']['input'];
};

export type Role = {
  __typename?: 'Role';
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  employees?: Maybe<Array<Employee>>;
  id: Scalars['Float']['output'];
  name: Scalars['String']['output'];
  permissions?: Maybe<Array<Permission>>;
  sys: Scalars['Boolean']['output'];
  updatedAt: Scalars['String']['output'];
  users?: Maybe<Array<User>>;
};

export type RoleArgs = {
  name: Scalars['String']['input'];
  permissions?: InputMaybe<Array<Scalars['Float']['input']>>;
};

export type SaleInput = {
  batch?: InputMaybe<Scalars['String']['input']>;
  hold?: InputMaybe<Scalars['Boolean']['input']>;
  itemId: Scalars['Float']['input'];
  quantity: Scalars['Float']['input'];
  remarks?: InputMaybe<Scalars['String']['input']>;
  unit: Scalars['String']['input'];
};

export type Schedule = {
  __typename?: 'Schedule';
  clinic?: Maybe<Clinic>;
  clinicId?: Maybe<Scalars['Float']['output']>;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  day: DayOfWeek;
  deleted: Scalars['Boolean']['output'];
  description?: Maybe<Scalars['String']['output']>;
  employee?: Maybe<Employee>;
  employeeId?: Maybe<Scalars['Float']['output']>;
  id: Scalars['Float']['output'];
  offTime: Scalars['String']['output'];
  onTime: Scalars['String']['output'];
  status: Status;
  updatedAt: Scalars['String']['output'];
};

export type ScheduleBulkArgs = {
  clinicId?: InputMaybe<Scalars['Float']['input']>;
  employeeId?: InputMaybe<Scalars['Float']['input']>;
  schedules: Array<ScheduleDetailsArgs>;
};

export type ScheduleDetailsArgs = {
  day: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  offTime: Scalars['String']['input'];
  onTime: Scalars['String']['input'];
};

export type ScheduleEditBulkArgs = {
  schedules: Array<ScheduleEditDetailsArgs>;
};

export type ScheduleEditDetailsArgs = {
  description: Scalars['String']['input'];
  offTime: Scalars['String']['input'];
  onTime: Scalars['String']['input'];
  scheduleId: Scalars['Float']['input'];
};

export type ServiceInput = {
  description?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  reference?: InputMaybe<Scalars['String']['input']>;
  sellingPrice: Scalars['Float']['input'];
};

export enum Status {
  Attended = 'ATTENDED',
  Canceled = 'CANCELED',
  Missed = 'MISSED',
  Pending = 'PENDING',
  Postponed = 'POSTPONED'
}

export type Store = {
  __typename?: 'Store';
  address: Scalars['String']['output'];
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  id: Scalars['Float']['output'];
  name: Scalars['String']['output'];
  primary: Scalars['Boolean']['output'];
  stockIn: Array<Inventory>;
  stockOut: Array<Inventory>;
  storeItemStocks?: Maybe<Array<StoreItemStock>>;
  storeKeepers?: Maybe<Array<Employee>>;
  updatedAt: Scalars['String']['output'];
};

export type StoreEditInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  companyId?: InputMaybe<Scalars['Float']['input']>;
  id: Scalars['Float']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  primary?: InputMaybe<Scalars['Boolean']['input']>;
};

export type StoreInput = {
  address: Scalars['String']['input'];
  name: Scalars['String']['input'];
  primary?: InputMaybe<Scalars['Boolean']['input']>;
};

export type StoreItemStock = {
  __typename?: 'StoreItemStock';
  batchId: Scalars['Float']['output'];
  batchStock?: Maybe<BatchStock>;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  id: Scalars['Float']['output'];
  item: Item;
  itemId: Scalars['Float']['output'];
  stock: Scalars['Float']['output'];
  store: Store;
  storeId: Scalars['Float']['output'];
  updatedAt: Scalars['String']['output'];
};

export type SyncConfig = {
  __typename?: 'SyncConfig';
  localCompanyId: Scalars['String']['output'];
  remoteApiKey: Scalars['String']['output'];
  remoteApiUrl: Scalars['String']['output'];
  syncInterval: Scalars['String']['output'];
  syncRetryAttempts: Scalars['String']['output'];
  syncRetryDelay: Scalars['String']['output'];
};

export type SyncConfigInput = {
  localCompanyId: Scalars['String']['input'];
  remoteApiKey: Scalars['String']['input'];
  remoteApiUrl: Scalars['String']['input'];
  syncInterval: Scalars['String']['input'];
  syncRetryAttempts: Scalars['String']['input'];
  syncRetryDelay: Scalars['String']['input'];
};

export type SyncError = {
  __typename?: 'SyncError';
  code?: Maybe<Scalars['String']['output']>;
  details?: Maybe<Scalars['String']['output']>;
  message: Scalars['String']['output'];
  stack?: Maybe<Scalars['String']['output']>;
};

export type SyncHistory = {
  __typename?: 'SyncHistory';
  company: Company;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['DateTime']['output'];
  direction: Scalars['String']['output'];
  entityName: Scalars['String']['output'];
  error?: Maybe<SyncError>;
  id: Scalars['Float']['output'];
  lastSyncTimestamp: Scalars['DateTime']['output'];
  recordsProcessed: Scalars['Float']['output'];
  status: Scalars['String']['output'];
  updatedAt: Scalars['DateTime']['output'];
};

export type Transfer = {
  __typename?: 'Transfer';
  batch: Scalars['String']['output'];
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  details?: Maybe<Scalars['String']['output']>;
  dispatched: Scalars['Boolean']['output'];
  granted: Scalars['Boolean']['output'];
  id: Scalars['Float']['output'];
  inventoryId: Scalars['Float']['output'];
  inventoryTransfer: Inventory;
  item: Item;
  itemId: Scalars['Float']['output'];
  price: Scalars['Float']['output'];
  quantity: Scalars['Float']['output'];
  received: Scalars['Boolean']['output'];
  updatedAt: Scalars['String']['output'];
};

export type TransferInput = {
  batch?: InputMaybe<Scalars['String']['input']>;
  itemId: Scalars['Float']['input'];
  quantity: Scalars['Float']['input'];
  unit: Scalars['String']['input'];
};

export type TransferResponse = {
  __typename?: 'TransferResponse';
  error?: Maybe<FieldError>;
  status: Scalars['Boolean']['output'];
  transfer?: Maybe<Transfer>;
};

export type Type = {
  __typename?: 'Type';
  category?: Maybe<Array<Category>>;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  description: Scalars['String']['output'];
  id: Scalars['Float']['output'];
  name: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
};

export type TypeArgs = {
  description: Scalars['String']['input'];
  name: Scalars['String']['input'];
};

export type TypeEditArgs = {
  categories: Array<Scalars['Float']['input']>;
  description: Scalars['String']['input'];
  name: Scalars['String']['input'];
};

export type UndetailedUser = {
  __typename?: 'UndetailedUser';
  email: Scalars['String']['output'];
  firstname: Scalars['String']['output'];
  image?: Maybe<Scalars['String']['output']>;
  lastname: Scalars['String']['output'];
};

export type Unit = {
  __typename?: 'Unit';
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  id: Scalars['Float']['output'];
  item: Item;
  itemId: Scalars['Float']['output'];
  name: Scalars['String']['output'];
  price: Scalars['Float']['output'];
  quantity: Scalars['Float']['output'];
  updatedAt: Scalars['String']['output'];
};

export type UnitInput = {
  itemId: Scalars['Float']['input'];
  name: Scalars['String']['input'];
  price: Scalars['Float']['input'];
  quantity: Scalars['Float']['input'];
};

export type UpdatePaymentInput = {
  amount?: InputMaybe<Scalars['Float']['input']>;
  autoRenew?: InputMaybe<Scalars['Boolean']['input']>;
  billingCycle?: InputMaybe<Scalars['String']['input']>;
  cancellationReason?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['DateTime']['input']>;
  features?: InputMaybe<Scalars['String']['input']>;
  lastPaymentDate?: InputMaybe<Scalars['DateTime']['input']>;
  maxUsers?: InputMaybe<Scalars['Float']['input']>;
  packageName?: InputMaybe<Scalars['String']['input']>;
  paymentReference?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['DateTime']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
};

export type User = {
  __typename?: 'User';
  address: Scalars['String']['output'];
  company: Company;
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  dateOfBirth: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  email?: Maybe<Scalars['String']['output']>;
  employee?: Maybe<Employee>;
  firstname: Scalars['String']['output'];
  gender: Scalars['String']['output'];
  id: Scalars['Float']['output'];
  image?: Maybe<Scalars['String']['output']>;
  lastname: Scalars['String']['output'];
  middlename: Scalars['String']['output'];
  permissions?: Maybe<Array<Permission>>;
  phone: Scalars['String']['output'];
  role: Role;
  roleId: Scalars['Float']['output'];
  status?: Maybe<Array<Category>>;
  updatedAt: Scalars['String']['output'];
};

export type UserResponse = {
  __typename?: 'UserResponse';
  error?: Maybe<FieldError>;
  token?: Maybe<Scalars['String']['output']>;
  user?: Maybe<User>;
};

export type Visit = {
  __typename?: 'Visit';
  active: Scalars['Boolean']['output'];
  bills: Array<Bill>;
  careTakerName?: Maybe<Scalars['String']['output']>;
  careTakerPhone?: Maybe<Scalars['String']['output']>;
  careTakerRelationship?: Maybe<Scalars['String']['output']>;
  checkInStatus: Scalars['String']['output'];
  checkInTime: Scalars['DateTime']['output'];
  checkInType: Scalars['String']['output'];
  checkOutStatus?: Maybe<Scalars['String']['output']>;
  checkOutTime: Scalars['DateTime']['output'];
  checkOutType?: Maybe<Scalars['String']['output']>;
  client: Patient;
  clientId: Scalars['Float']['output'];
  companyId: Scalars['Float']['output'];
  confirmedDx?: Maybe<Scalars['String']['output']>;
  consultation?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['String']['output'];
  currentLocation?: Maybe<Scalars['String']['output']>;
  deleted: Scalars['Boolean']['output'];
  folioId?: Maybe<Scalars['String']['output']>;
  folioNumber?: Maybe<Scalars['String']['output']>;
  id: Scalars['Float']['output'];
  insuranceAuthNumber?: Maybe<Scalars['String']['output']>;
  insuranceCardNumber?: Maybe<Scalars['String']['output']>;
  insuranceId?: Maybe<Scalars['String']['output']>;
  insuranceProvider?: Maybe<Scalars['String']['output']>;
  insuranceSchemeId?: Maybe<Scalars['String']['output']>;
  insuranceStatus?: Maybe<Scalars['String']['output']>;
  insuranceUserId?: Maybe<Scalars['String']['output']>;
  preliminaryDx?: Maybe<Scalars['String']['output']>;
  productCode?: Maybe<Scalars['String']['output']>;
  reason: Scalars['String']['output'];
  referralType: Scalars['String']['output'];
  status: Scalars['String']['output'];
  supportingFile?: Maybe<Scalars['String']['output']>;
  ticket: Scalars['String']['output'];
  type: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  visitToClinics: Array<VisitToClinic>;
  vitals?: Maybe<Vitals>;
};

export type VisitInputArgs = {
  careTakerName: Scalars['String']['input'];
  careTakerPhone: Scalars['String']['input'];
  careTakerRelationship: Scalars['String']['input'];
  checkInType: Scalars['String']['input'];
  clientId: Scalars['Float']['input'];
  clinicId: Scalars['Float']['input'];
  doctorId?: InputMaybe<Scalars['Float']['input']>;
  insuranceAuthNumber?: InputMaybe<Scalars['String']['input']>;
  insuranceCardNumber?: InputMaybe<Scalars['String']['input']>;
  insuranceId?: InputMaybe<Scalars['String']['input']>;
  insuranceProvider?: InputMaybe<Scalars['String']['input']>;
  insuranceSchemeId?: InputMaybe<Scalars['String']['input']>;
  insuranceStatus?: InputMaybe<Scalars['String']['input']>;
  insuranceUserId?: InputMaybe<Scalars['String']['input']>;
  reason: Scalars['String']['input'];
  referralType: Scalars['String']['input'];
  status: Scalars['String']['input'];
  ticket: Scalars['String']['input'];
  type: Scalars['String']['input'];
  uploadedFile?: InputMaybe<Scalars['String']['input']>;
};

export type VisitResponse = {
  __typename?: 'VisitResponse';
  error?: Maybe<FieldError>;
  visit?: Maybe<Visit>;
};

export type VisitToClinic = {
  __typename?: 'VisitToClinic';
  attendingEmployee: Employee;
  checkInTime: Scalars['DateTime']['output'];
  checkOutTime: Scalars['DateTime']['output'];
  clinic: Clinic;
  clinicId: Scalars['Float']['output'];
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  employeeId?: Maybe<Scalars['Float']['output']>;
  id: Scalars['Float']['output'];
  updatedAt: Scalars['String']['output'];
  visit: Visit;
  visitId: Scalars['Float']['output'];
};

export type Vitals = {
  __typename?: 'Vitals';
  bloodGlucose: Scalars['String']['output'];
  bodyTemperature: Scalars['String']['output'];
  companyId: Scalars['Float']['output'];
  createdAt: Scalars['String']['output'];
  deleted: Scalars['Boolean']['output'];
  diastolicPressure: Scalars['String']['output'];
  height: Scalars['String']['output'];
  id: Scalars['Float']['output'];
  oxygenSaturation: Scalars['String']['output'];
  pulseRate: Scalars['String']['output'];
  respirationRate: Scalars['String']['output'];
  systolicPressure: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
  visit: Visit;
  visitId: Scalars['Float']['output'];
  weight: Scalars['String']['output'];
};

export type VitalsInputArgs = {
  bloodGlucose: Scalars['Float']['input'];
  bodyTemperature: Scalars['Float']['input'];
  diastolicPressure: Scalars['Float']['input'];
  height: Scalars['Float']['input'];
  oxygenSaturation: Scalars['Float']['input'];
  pulseRate: Scalars['Float']['input'];
  respirationRate: Scalars['Float']['input'];
  systolicPressure: Scalars['Float']['input'];
  visitId: Scalars['Float']['input'];
  weight: Scalars['Float']['input'];
};

export type WriteOffInput = {
  batch?: InputMaybe<Scalars['String']['input']>;
  itemId: Scalars['Float']['input'];
  locationId: Scalars['Float']['input'];
  quantity: Scalars['Float']['input'];
  reason?: InputMaybe<Scalars['String']['input']>;
  unit: Scalars['String']['input'];
};

export type ErrorFragment = { __typename?: 'FieldError', target: string, message: string };

export type MeFragment = { __typename?: 'User', id: number, firstname: string, middlename: string, lastname: string, email?: string | null, phone: string, image?: string | null, companyId: number, company: { __typename?: 'Company', id: number, name: string }, role: { __typename?: 'Role', id: number, name: string }, employee?: { __typename?: 'Employee', id: number, status: string, designation: string, department?: { __typename?: 'Department', id: number, name: string } | null } | null, permissions?: Array<{ __typename?: 'Permission', id: number, name: string }> | null };

export type BooleanResponseFragment = { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null };

export type ClientFragment = { __typename?: 'Patient', id: number, firstname: string, lastname: string, middlename: string, phone: string, email?: string | null, status: string, dateOfBirth: string, nationalId?: string | null, religion: string, gender: string, fileNumber?: string | null, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceProvider: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, nextOfKinName: string, nextOfKinPhone: string, nextOfKinRelationship: string, address: { __typename?: 'Address', country: string, city: string, street: string, ward: string, district: string } };

export type RegisterCompanyMutationVariables = Exact<{
  params: RegisterCompanyArgs;
}>;


export type RegisterCompanyMutation = { __typename?: 'Mutation', registerCompany: { __typename?: 'BooleanResponseId', id?: number | null, status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddStoreMutationVariables = Exact<{
  args: StoreInput;
}>;


export type AddStoreMutation = { __typename?: 'Mutation', addStore: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditStoreMutationVariables = Exact<{
  args: StoreEditInput;
}>;


export type EditStoreMutation = { __typename?: 'Mutation', editStore: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddSchedulesMutationVariables = Exact<{
  args: ScheduleBulkArgs;
}>;


export type AddSchedulesMutation = { __typename?: 'Mutation', addSchedules: { __typename?: 'BulkScheduleResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null, schedules?: Array<{ __typename?: 'Schedule', id: number, day: DayOfWeek, onTime: string, offTime: string, description?: string | null, clinicId?: number | null, employeeId?: number | null }> | null } };

export type EditSchedulesMutationVariables = Exact<{
  args: ScheduleEditBulkArgs;
}>;


export type EditSchedulesMutation = { __typename?: 'Mutation', editSchedules: { __typename?: 'BulkScheduleResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null, schedules?: Array<{ __typename?: 'Schedule', id: number, day: DayOfWeek, onTime: string, offTime: string, description?: string | null, clinicId?: number | null, employeeId?: number | null }> | null } };

export type AddDepartmentMutationVariables = Exact<{
  params: DepartmentInputArgs;
}>;


export type AddDepartmentMutation = { __typename?: 'Mutation', addDepartment: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditDepartmentMutationVariables = Exact<{
  id: Scalars['Float']['input'];
  params: DepartmentInputArgs;
}>;


export type EditDepartmentMutation = { __typename?: 'Mutation', editDepartment: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddClinicMutationVariables = Exact<{
  params: ClinicInputArgs;
}>;


export type AddClinicMutation = { __typename?: 'Mutation', addClinic: { __typename?: 'ClinicResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null, clinic?: { __typename?: 'Clinic', id: number, name: string, description: string, status: string, clinicType: string, size: number } | null } };

export type EditClinicMutationVariables = Exact<{
  id: Scalars['Int']['input'];
  params: ClinicEditArgs;
}>;


export type EditClinicMutation = { __typename?: 'Mutation', editClinic: { __typename?: 'ClinicResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null, clinic?: { __typename?: 'Clinic', id: number, name: string, description: string, status: string, clinicType: string, size: number } | null } };

export type DeleteClinicMutationVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type DeleteClinicMutation = { __typename?: 'Mutation', deleteClinic: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type SetHeadOfDepartmentMutationVariables = Exact<{
  departmentId: Scalars['Int']['input'];
  employeeId: Scalars['Int']['input'];
}>;


export type SetHeadOfDepartmentMutation = { __typename?: 'Mutation', setHeadOfDepartment: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type RegisterEmployeeMutationVariables = Exact<{
  params: RegisterEmployeeArgs;
}>;


export type RegisterEmployeeMutation = { __typename?: 'Mutation', registerEmployee: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ImportItemMutationVariables = Exact<{
  args: ImportInput;
}>;


export type ImportItemMutation = { __typename?: 'Mutation', importItem: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddItemMutationVariables = Exact<{
  args: ItemInput;
}>;


export type AddItemMutation = { __typename?: 'Mutation', addItem: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddServiceMutationVariables = Exact<{
  args: ServiceInput;
}>;


export type AddServiceMutation = { __typename?: 'Mutation', addService: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type WriteOffItemsMutationVariables = Exact<{
  args: Array<WriteOffInput> | WriteOffInput;
}>;


export type WriteOffItemsMutation = { __typename?: 'Mutation', writeOffItems: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type DispatchItemsMutationVariables = Exact<{
  args: Array<DispatchInput> | DispatchInput;
}>;


export type DispatchItemsMutation = { __typename?: 'Mutation', dispatchItems: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type InstantTransferMutationVariables = Exact<{
  args: Array<TransferInput> | TransferInput;
  sourceStore: Scalars['Float']['input'];
  destinationStore: Scalars['Float']['input'];
}>;


export type InstantTransferMutation = { __typename?: 'Mutation', instantTransfer: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type QuickSaleMutationVariables = Exact<{
  args: Array<SaleInput> | SaleInput;
}>;


export type QuickSaleMutation = { __typename?: 'Mutation', quickSale: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ServePayLaterMutationVariables = Exact<{
  args: Array<SaleInput> | SaleInput;
  servedTo?: InputMaybe<Scalars['Float']['input']>;
  customerTag?: InputMaybe<Scalars['String']['input']>;
}>;


export type ServePayLaterMutation = { __typename?: 'Mutation', servePayLater: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type UpdateBillMutationVariables = Exact<{
  inventoryId: Scalars['Float']['input'];
  args: Array<SaleInput> | SaleInput;
}>;


export type UpdateBillMutation = { __typename?: 'Mutation', updateBill: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditBillItemMutationVariables = Exact<{
  inventoryId: Scalars['Float']['input'];
  transferId: Scalars['Float']['input'];
  newQuantity: Scalars['Float']['input'];
}>;


export type EditBillItemMutation = { __typename?: 'Mutation', editBillItem: { __typename?: 'TransferResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null, transfer?: { __typename?: 'Transfer', id: number, itemId: number, inventoryId: number, quantity: number, details?: string | null, price: number, dispatched: boolean } | null } };

export type DeleteBillItemMutationVariables = Exact<{
  inventoryId: Scalars['Float']['input'];
  transferId: Scalars['Float']['input'];
}>;


export type DeleteBillItemMutation = { __typename?: 'Mutation', deleteBillItem: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ClearBillMutationVariables = Exact<{
  saleId: Scalars['Float']['input'];
}>;


export type ClearBillMutation = { __typename?: 'Mutation', clearBill: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ServePendingOrderMutationVariables = Exact<{
  transferId: Scalars['Float']['input'];
}>;


export type ServePendingOrderMutation = { __typename?: 'Mutation', servePendingOrder: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type TransferItemsMutationVariables = Exact<{
  args: Array<DispatchInput> | DispatchInput;
}>;


export type TransferItemsMutation = { __typename?: 'Mutation', transferItems: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddItemsFromExcelMutationVariables = Exact<{
  args: BulkItemInput;
}>;


export type AddItemsFromExcelMutation = { __typename?: 'Mutation', addItemsFromExcel: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditItemMutationVariables = Exact<{
  args: ItemInput;
  id: Scalars['Float']['input'];
}>;


export type EditItemMutation = { __typename?: 'Mutation', editItem: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditServiceMutationVariables = Exact<{
  args: ServiceInput;
  id: Scalars['Float']['input'];
}>;


export type EditServiceMutation = { __typename?: 'Mutation', editService: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type DeleteItemMutationVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type DeleteItemMutation = { __typename?: 'Mutation', deleteItem: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ChangeInventoryApprovalStatusMutationVariables = Exact<{
  inventoryId: Scalars['Float']['input'];
}>;


export type ChangeInventoryApprovalStatusMutation = { __typename?: 'Mutation', changeInventoryApprovalStatus: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ClearServedBillMutationVariables = Exact<{
  inventoryId: Scalars['Float']['input'];
}>;


export type ClearServedBillMutation = { __typename?: 'Mutation', clearServedBill: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ChangeInventoryDispatchedStatusMutationVariables = Exact<{
  inventoryId: Scalars['Float']['input'];
}>;


export type ChangeInventoryDispatchedStatusMutation = { __typename?: 'Mutation', changeInventoryDispatchedStatus: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ChangeInventorySoldStatusMutationVariables = Exact<{
  inventoryId: Scalars['Float']['input'];
}>;


export type ChangeInventorySoldStatusMutation = { __typename?: 'Mutation', changeInventorySoldStatus: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ChangeInventoryReceivedStatusMutationVariables = Exact<{
  inventoryId: Scalars['Float']['input'];
}>;


export type ChangeInventoryReceivedStatusMutation = { __typename?: 'Mutation', changeInventoryReceivedStatus: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type RegisterPatientMutationVariables = Exact<{
  params: RegisterPatientArgs;
}>;


export type RegisterPatientMutation = { __typename?: 'Mutation', registerPatient: { __typename?: 'PatientResponse', error?: { __typename?: 'FieldError', target: string, message: string } | null, patient?: { __typename?: 'Patient', id: number, firstname: string, lastname: string, middlename: string, phone: string, email?: string | null, status: string, dateOfBirth: string, nationalId?: string | null, religion: string, gender: string, fileNumber?: string | null, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceProvider: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, nextOfKinName: string, nextOfKinPhone: string, nextOfKinRelationship: string, address: { __typename?: 'Address', country: string, city: string, street: string, ward: string, district: string } } | null } };

export type EditPatientMutationVariables = Exact<{
  id: Scalars['Float']['input'];
  params: RegisterPatientArgs;
}>;


export type EditPatientMutation = { __typename?: 'Mutation', editPatient: { __typename?: 'PatientResponse', error?: { __typename?: 'FieldError', target: string, message: string } | null, patient?: { __typename?: 'Patient', id: number, firstname: string, lastname: string, middlename: string, phone: string, email?: string | null, status: string, dateOfBirth: string, nationalId?: string | null, religion: string, gender: string, fileNumber?: string | null, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceProvider: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, nextOfKinName: string, nextOfKinPhone: string, nextOfKinRelationship: string, address: { __typename?: 'Address', country: string, city: string, street: string, ward: string, district: string } } | null } };

export type AddVisitMutationVariables = Exact<{
  params: VisitInputArgs;
}>;


export type AddVisitMutation = { __typename?: 'Mutation', addVisit: { __typename?: 'VisitResponse', error?: { __typename?: 'FieldError', target: string, message: string } | null, visit?: { __typename?: 'Visit', id: number, status: string, type: string, reason: string, consultation?: string | null, currentLocation?: string | null, clientId: number, client: { __typename?: 'Patient', id: number, firstname: string, middlename: string, lastname: string, status: string, email?: string | null, phone: string, insuranceProvider: string, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, gender: string, dateOfBirth: string, fileNumber?: string | null, nationalId?: string | null, bloodGroup?: string | null, registererId?: number | null } } | null } };

export type AddVitalsMutationVariables = Exact<{
  params: VitalsInputArgs;
}>;


export type AddVitalsMutation = { __typename?: 'Mutation', addVitals: { __typename?: 'VisitResponse', error?: { __typename?: 'FieldError', target: string, message: string } | null, visit?: { __typename?: 'Visit', id: number, status: string, type: string, reason: string, consultation?: string | null, currentLocation?: string | null, clientId: number, client: { __typename?: 'Patient', id: number, firstname: string, middlename: string, lastname: string, status: string, email?: string | null, phone: string, insuranceProvider: string, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, gender: string, dateOfBirth: string, fileNumber?: string | null, nationalId?: string | null, bloodGroup?: string | null, registererId?: number | null }, vitals?: { __typename?: 'Vitals', id: number, height: string, weight: string, pulseRate: string, bodyTemperature: string, respirationRate: string, oxygenSaturation: string, systolicPressure: string, diastolicPressure: string, bloodGlucose: string } | null } | null } };

export type DeleteTypeMutationVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type DeleteTypeMutation = { __typename?: 'Mutation', deleteType: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddTypeMutationVariables = Exact<{
  args: TypeArgs;
}>;


export type AddTypeMutation = { __typename?: 'Mutation', addType: { __typename?: 'BooleanResponseWithType', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null, data?: { __typename?: 'Type', id: number, name: string, description: string } | null } };

export type EditTypeMutationVariables = Exact<{
  id: Scalars['Float']['input'];
  args: TypeEditArgs;
}>;


export type EditTypeMutation = { __typename?: 'Mutation', editType: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddPermissionMutationVariables = Exact<{
  name: Scalars['String']['input'];
  userId?: InputMaybe<Scalars['Float']['input']>;
  roleId?: InputMaybe<Scalars['Float']['input']>;
}>;


export type AddPermissionMutation = { __typename?: 'Mutation', addPermission: { __typename?: 'PermissionResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null, permission?: { __typename?: 'Permission', id: number, name: string } | null } };

export type RemovePermissionMutationVariables = Exact<{
  name: Scalars['String']['input'];
  userId?: InputMaybe<Scalars['Float']['input']>;
  roleId?: InputMaybe<Scalars['Float']['input']>;
}>;


export type RemovePermissionMutation = { __typename?: 'Mutation', removePermission: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditPermissionMutationVariables = Exact<{
  id: Scalars['Float']['input'];
  name: Scalars['String']['input'];
}>;


export type EditPermissionMutation = { __typename?: 'Mutation', editPermission: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type DeletePermissionMutationVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type DeletePermissionMutation = { __typename?: 'Mutation', deletePermission: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddFeatureMutationVariables = Exact<{
  name: Scalars['String']['input'];
  companyId: Scalars['Float']['input'];
}>;


export type AddFeatureMutation = { __typename?: 'Mutation', addFeature: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditFeatureMutationVariables = Exact<{
  id: Scalars['Float']['input'];
  name: Scalars['String']['input'];
}>;


export type EditFeatureMutation = { __typename?: 'Mutation', editFeature: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type DeleteFeatureMutationVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type DeleteFeatureMutation = { __typename?: 'Mutation', deleteFeature: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddRoleMutationVariables = Exact<{
  name: Scalars['String']['input'];
}>;


export type AddRoleMutation = { __typename?: 'Mutation', addRole: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditRoleMutationVariables = Exact<{
  id: Scalars['Float']['input'];
  args: RoleArgs;
}>;


export type EditRoleMutation = { __typename?: 'Mutation', editRole: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type DeleteRoleMutationVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type DeleteRoleMutation = { __typename?: 'Mutation', deleteRole: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddCategoryMutationVariables = Exact<{
  args: CategoryArgs;
}>;


export type AddCategoryMutation = { __typename?: 'Mutation', addCategory: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddCategoryWithTypeNameMutationVariables = Exact<{
  args: CategoryTypeArgs;
}>;


export type AddCategoryWithTypeNameMutation = { __typename?: 'Mutation', addCategoryWithTypeName: { __typename?: 'CategoryResponse', error?: { __typename?: 'FieldError', target: string, message: string } | null, category?: { __typename?: 'Category', id: number, name: string } | null } };

export type EditCategoryMutationVariables = Exact<{
  id: Scalars['Float']['input'];
  args: CategoryArgs;
}>;


export type EditCategoryMutation = { __typename?: 'Mutation', editCategory: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditCategoryByNameMutationVariables = Exact<{
  name: Scalars['String']['input'];
  args: CategoryArgs;
}>;


export type EditCategoryByNameMutation = { __typename?: 'Mutation', editCategoryByName: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type DeleteCategoryMutationVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type DeleteCategoryMutation = { __typename?: 'Mutation', deleteCategory: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AddExpenseMutationVariables = Exact<{
  args: ExpenseInput;
}>;


export type AddExpenseMutation = { __typename?: 'Mutation', addExpense: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditExpenseMutationVariables = Exact<{
  id: Scalars['Float']['input'];
  args: ExpenseInput;
}>;


export type EditExpenseMutation = { __typename?: 'Mutation', editExpense: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type AuthorizeExpenseMutationVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type AuthorizeExpenseMutation = { __typename?: 'Mutation', authorizeExpense: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type DeleteExpenseMutationVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type DeleteExpenseMutation = { __typename?: 'Mutation', deleteExpense: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type CreatePaymentMutationVariables = Exact<{
  input: CreatePaymentInput;
}>;


export type CreatePaymentMutation = { __typename?: 'Mutation', createPayment: { __typename?: 'Payment', id: number, status: PaymentStatus, packageName: string, startDate: string, endDate: string } };

export type ChangePaymentStatusMutationVariables = Exact<{
  id: Scalars['Float']['input'];
  status: Scalars['String']['input'];
}>;


export type ChangePaymentStatusMutation = { __typename?: 'Mutation', changePaymentStatus: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ForgotPasswordMutationVariables = Exact<{
  email: Scalars['String']['input'];
}>;


export type ForgotPasswordMutation = { __typename?: 'Mutation', forgotPassword: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type LoginMutationVariables = Exact<{
  params: EmailPasswordArgs;
}>;


export type LoginMutation = { __typename?: 'Mutation', login: { __typename?: 'UserResponse', token?: string | null, user?: { __typename?: 'User', id: number, company: { __typename?: 'Company', id: number } } | null, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type LogoutMutationVariables = Exact<{ [key: string]: never; }>;


export type LogoutMutation = { __typename?: 'Mutation', logout: boolean };

export type ResetPasswordMutationVariables = Exact<{
  token: Scalars['String']['input'];
  newPassword: Scalars['String']['input'];
}>;


export type ResetPasswordMutation = { __typename?: 'Mutation', resetPassword: { __typename?: 'UserResponse', user?: { __typename?: 'User', id: number, firstname: string, middlename: string, lastname: string, email?: string | null, phone: string, image?: string | null, companyId: number, company: { __typename?: 'Company', id: number, name: string }, role: { __typename?: 'Role', id: number, name: string }, employee?: { __typename?: 'Employee', id: number, status: string, designation: string, department?: { __typename?: 'Department', id: number, name: string } | null } | null, permissions?: Array<{ __typename?: 'Permission', id: number, name: string }> | null } | null, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type RegisterMutationVariables = Exact<{
  params: RegisterUserArgs;
}>;


export type RegisterMutation = { __typename?: 'Mutation', register: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type EditUserMutationVariables = Exact<{
  id: Scalars['Float']['input'];
  params: EditUserArgs;
}>;


export type EditUserMutation = { __typename?: 'Mutation', editUser: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ChangeEmployeeStatusMutationVariables = Exact<{
  employeeId: Scalars['Float']['input'];
  status: Scalars['String']['input'];
}>;


export type ChangeEmployeeStatusMutation = { __typename?: 'Mutation', changeEmployeeStatus: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ChangeEmployeeRoleMutationVariables = Exact<{
  employeeId: Scalars['Float']['input'];
  companyRole: Scalars['Float']['input'];
  departmentId: Scalars['Float']['input'];
  designation: Scalars['String']['input'];
}>;


export type ChangeEmployeeRoleMutation = { __typename?: 'Mutation', changeEmployeeRole: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type ChangePasswordMutationVariables = Exact<{
  currentPassword: Scalars['String']['input'];
  newPassword: Scalars['String']['input'];
}>;


export type ChangePasswordMutation = { __typename?: 'Mutation', changePassword: { __typename?: 'BooleanResponse', status: boolean, error?: { __typename?: 'FieldError', target: string, message: string } | null } };

export type GetCompanyQueryVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type GetCompanyQuery = { __typename?: 'Query', getCompany?: { __typename?: 'Company', id: number, name: string, tinNumber: string, registrationNumber: string, type: string, location: string, employees: Array<{ __typename?: 'Employee', id: number, role: { __typename?: 'Role', name: string } }>, features?: Array<{ __typename?: 'Feature', id: number, name: string }> | null } | null };

export type GetCompaniesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetCompaniesQuery = { __typename?: 'Query', getCompanies: Array<{ __typename?: 'Company', id: number, name: string, tinNumber: string, registrationNumber: string, type: string, location: string, employees: Array<{ __typename?: 'Employee', id: number, role: { __typename?: 'Role', name: string } }>, features?: Array<{ __typename?: 'Feature', id: number, name: string }> | null, payments?: Array<{ __typename?: 'Payment', id: number, status: PaymentStatus, startDate: string, endDate: string, packageName: string, billingCycle: BillingCycle, amount: number }> | null }> };

export type GetSchedulesQueryVariables = Exact<{
  ownerId: Scalars['Int']['input'];
  owner: Scalars['String']['input'];
}>;


export type GetSchedulesQuery = { __typename?: 'Query', getSchedules: Array<{ __typename?: 'Schedule', id: number, onTime: string, offTime: string, day: DayOfWeek, description?: string | null, clinicId?: number | null, employeeId?: number | null }> };

export type GetEmployeesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetEmployeesQuery = { __typename?: 'Query', getEmployees: Array<{ __typename?: 'User', id: number, firstname: string, middlename: string, lastname: string, email?: string | null, phone: string, image?: string | null, companyId: number, company: { __typename?: 'Company', id: number, name: string }, role: { __typename?: 'Role', id: number, name: string }, employee?: { __typename?: 'Employee', id: number, status: string, designation: string, department?: { __typename?: 'Department', id: number, name: string } | null } | null, permissions?: Array<{ __typename?: 'Permission', id: number, name: string }> | null }> };

export type GetUndetailedEmployeesQueryVariables = Exact<{
  companyId: Scalars['Float']['input'];
}>;


export type GetUndetailedEmployeesQuery = { __typename?: 'Query', getUndetailedEmployees: Array<{ __typename?: 'UndetailedUser', firstname: string, lastname: string, email: string, image?: string | null }> };

export type GetDepartmentsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetDepartmentsQuery = { __typename?: 'Query', getDepartments: Array<{ __typename?: 'Department', id: number, name: string, type?: string | null, description: string, status?: string | null, parentId?: number | null, headOfDepartment?: { __typename?: 'Employee', id: number } | null, employees?: Array<{ __typename?: 'Employee', id: number }> | null, clinics?: Array<{ __typename?: 'Clinic', id: number, name: string, clinicType: string, size: number, status: string, description: string }> | null }> };

export type GetClinicsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetClinicsQuery = { __typename?: 'Query', getClinics: Array<{ __typename?: 'Clinic', id: number, name: string, description: string, status: string, clinicType: string, size: number, department?: { __typename?: 'Department', id: number, name: string } | null, leader?: { __typename?: 'Employee', id: number } | null }> };

export type GetClinicQueryVariables = Exact<{
  id: Scalars['Int']['input'];
}>;


export type GetClinicQuery = { __typename?: 'Query', getClinic?: { __typename?: 'Clinic', id: number, name: string, description: string, status: string, clinicType: string, size: number, department?: { __typename?: 'Department', id: number, name: string } | null, leader?: { __typename?: 'Employee', id: number } | null } | null };

export type GetUsersQueryVariables = Exact<{
  roles?: InputMaybe<Array<Scalars['Float']['input']> | Scalars['Float']['input']>;
}>;


export type GetUsersQuery = { __typename?: 'Query', getUsers: Array<{ __typename?: 'User', id: number, firstname: string, middlename: string, lastname: string, email?: string | null, phone: string, companyId: number, company: { __typename?: 'Company', id: number, name: string }, role: { __typename?: 'Role', id: number, name: string }, employee?: { __typename?: 'Employee', id: number, status: string, designation: string, role: { __typename?: 'Role', id: number, name: string } } | null }> };

export type GetUserQueryVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type GetUserQuery = { __typename?: 'Query', getUser?: { __typename?: 'User', id: number, firstname: string, middlename: string, lastname: string, email?: string | null, phone: string, image?: string | null, companyId: number, company: { __typename?: 'Company', id: number, name: string }, role: { __typename?: 'Role', id: number, name: string }, employee?: { __typename?: 'Employee', id: number, status: string, designation: string, role: { __typename?: 'Role', id: number, name: string } } | null, permissions?: Array<{ __typename?: 'Permission', id: number, name: string }> | null } | null };

export type GetAllItemsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetAllItemsQuery = { __typename?: 'Query', getAllItems: Array<{ __typename?: 'Item', id: number, name: string, type: string, barcode?: string | null, description: string, image: string, sellingPrice: number, reorder: number, reference: string, internal: boolean, unit: string, stock: number, units: Array<{ __typename?: 'Unit', id: number, name: string, quantity: number, price: number }> }> };

export type GetAllServicesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetAllServicesQuery = { __typename?: 'Query', getAllServices: Array<{ __typename?: 'Item', id: number, name: string, description: string, sellingPrice: number, reference: string }> };

export type GetStoreItemsQueryVariables = Exact<{
  storeId: Scalars['Float']['input'];
}>;


export type GetStoreItemsQuery = { __typename?: 'Query', getStoreItems: Array<{ __typename?: 'Item', id: number, name: string, type: string, image: string, barcode?: string | null, description: string, sellingPrice: number, reorder: number, reference: string, internal: boolean, unit: string, stock: number, units: Array<{ __typename?: 'Unit', id: number, name: string, quantity: number, price: number }> }> };

export type GetInternalItemsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetInternalItemsQuery = { __typename?: 'Query', getInternalItems: Array<{ __typename?: 'Item', id: number, name: string, type: string, image: string, barcode?: string | null, description: string, reorder: number, reference: string, internal: boolean, unit: string, stock: number, units: Array<{ __typename?: 'Unit', id: number, name: string, quantity: number, price: number }> }> };

export type GetMerchandiseItemsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetMerchandiseItemsQuery = { __typename?: 'Query', getMerchandiseItems: Array<{ __typename?: 'Item', id: number, name: string, type: string, barcode?: string | null, image: string, description: string, sellingPrice: number, reorder: number, reference: string, internal: boolean, unit: string, stock: number, units: Array<{ __typename?: 'Unit', id: number, name: string, quantity: number, price: number }> }> };

export type GetItemQueryVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type GetItemQuery = { __typename?: 'Query', getItem: { __typename?: 'Item', id: number, name: string, type: string, barcode?: string | null, image: string, description: string, internal: boolean, reorder: number, reference: string, unit: string, stock: number, sellingPrice: number, units: Array<{ __typename?: 'Unit', id: number, name: string, quantity: number, price: number }>, imports?: Array<{ __typename?: 'Import', id: number, importDate: string, supplier: string, quantity: number, importPrice: number, sellingPrice: number }> | null, inventoryTransfers?: Array<{ __typename?: 'Inventory', id: number, type: string, details?: string | null, transferDate: string, sourceStoreId?: number | null, destinationStoreId?: number | null }> | null, transfers?: Array<{ __typename?: 'Transfer', id: number, inventoryId: number, itemId: number, quantity: number }> | null } };

export type GetSalesQueryVariables = Exact<{
  date?: InputMaybe<Scalars['DateTime']['input']>;
}>;


export type GetSalesQuery = { __typename?: 'Query', getSales: Array<{ __typename?: 'Inventory', id: number, createdAt: string, details?: string | null, type: string, granted: boolean, dispatched: boolean, sourceStoreId?: number | null, keeper?: { __typename?: 'Employee', id: number, userId: number } | null, approver?: { __typename?: 'Employee', id: number, userId: number } | null, bill?: { __typename?: 'Bill', amount: number, cleared: boolean, paymentType: string } | null, transfers: Array<{ __typename?: 'Transfer', id: number, itemId: number, quantity: number, details?: string | null }>, items: Array<{ __typename?: 'Item', id: number, name: string, unit: string, sellingPrice: number, type: string, reference: string, description: string, imports?: Array<{ __typename?: 'Import', id: number, importDate: string, importPrice: number }> | null }> }> };

export type GetSalesPosQueryVariables = Exact<{ [key: string]: never; }>;


export type GetSalesPosQuery = { __typename?: 'Query', getSalesPOS: Array<{ __typename?: 'Inventory', id: number, createdAt: string, details?: string | null, type: string, granted: boolean, dispatched: boolean, sourceStoreId?: number | null, customerTag?: string | null, sourceStore?: { __typename?: 'Store', id: number, name: string } | null, consumer?: { __typename?: 'Employee', id: number, userId: number, user: { __typename?: 'User', id: number, firstname: string, lastname: string } } | null, keeper?: { __typename?: 'Employee', id: number, userId: number, user: { __typename?: 'User', id: number, firstname: string, lastname: string } } | null, bill?: { __typename?: 'Bill', amount: number, cleared: boolean, paymentType: string } | null, transfers: Array<{ __typename?: 'Transfer', id: number, itemId: number, quantity: number, details?: string | null, price: number, dispatched: boolean }>, items: Array<{ __typename?: 'Item', id: number, name: string, unit: string, sellingPrice: number, type: string, reference: string, description: string }> }> };

export type GetOpenTabsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetOpenTabsQuery = { __typename?: 'Query', getOpenTabs: Array<{ __typename?: 'Inventory', id: number, createdAt: string, details?: string | null, type: string, granted: boolean, dispatched: boolean, sourceStoreId?: number | null, customerTag?: string | null, sourceStore?: { __typename?: 'Store', id: number, name: string } | null, consumer?: { __typename?: 'Employee', id: number, userId: number, user: { __typename?: 'User', id: number, firstname: string, lastname: string } } | null, keeper?: { __typename?: 'Employee', id: number, userId: number, user: { __typename?: 'User', id: number, firstname: string, lastname: string } } | null, bill?: { __typename?: 'Bill', createdAt: string, amount: number, cleared: boolean, paymentType: string } | null, transfers: Array<{ __typename?: 'Transfer', id: number, itemId: number, inventoryId: number, quantity: number, details?: string | null, price: number, dispatched: boolean }>, items: Array<{ __typename?: 'Item', id: number, name: string, unit: string, sellingPrice: number, type: string, reference: string, description: string, stock: number }> }> };

export type GetStoresQueryVariables = Exact<{ [key: string]: never; }>;


export type GetStoresQuery = { __typename?: 'Query', getStores: Array<{ __typename?: 'Store', id: number, name: string, primary: boolean, address: string, storeKeepers?: Array<{ __typename?: 'Employee', id: number }> | null }> };

export type GetInventoryTransfersQueryVariables = Exact<{
  type?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetInventoryTransfersQuery = { __typename?: 'Query', getInventoryTransfers: Array<{ __typename?: 'Inventory', id: number, details?: string | null, type: string, granted: boolean, received: boolean, transferDate: string, sourceStore?: { __typename?: 'Store', id: number, name: string } | null, destinationStore?: { __typename?: 'Store', id: number, name: string } | null }> };

export type GetInventoryTransferQueryVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type GetInventoryTransferQuery = { __typename?: 'Query', getInventoryTransfer?: { __typename?: 'Inventory', id: number, details?: string | null, type: string, granted: boolean, received: boolean, transferDate: string, sourceStore?: { __typename?: 'Store', id: number, name: string } | null, destinationStore?: { __typename?: 'Store', id: number, name: string } | null, keeper?: { __typename?: 'Employee', id: number, userId: number } | null, consumer?: { __typename?: 'Employee', id: number, userId: number } | null, transfers: Array<{ __typename?: 'Transfer', id: number, itemId: number, quantity: number }>, items: Array<{ __typename?: 'Item', id: number, name: string, unit: string, sellingPrice: number, type: string }> } | null };

export type GetItemTransfersQueryVariables = Exact<{
  itemId: Scalars['Float']['input'];
  type?: InputMaybe<Scalars['String']['input']>;
}>;


export type GetItemTransfersQuery = { __typename?: 'Query', getItemTransfers: Array<{ __typename?: 'Transfer', id: number, details?: string | null, quantity: number, inventoryTransfer: { __typename?: 'Inventory', id: number, updatedAt: string, details?: string | null, type: string, transferDate: string, sourceStoreId?: number | null, destinationStoreId?: number | null }, item: { __typename?: 'Item', id: number, name: string, unit: string } }> };

export type GetWriteOffsByCompanyQueryVariables = Exact<{ [key: string]: never; }>;


export type GetWriteOffsByCompanyQuery = { __typename?: 'Query', getWriteOffsByCompany: Array<{ __typename?: 'Transfer', id: number, createdAt: string, quantity: number, details?: string | null, item: { __typename?: 'Item', id: number, name: string, unit: string } }> };

export type GetDispatchesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetDispatchesQuery = { __typename?: 'Query', getDispatches: Array<{ __typename?: 'Inventory', id: number, updatedAt: string, transferDate: string, granted: boolean, dispatched: boolean, received: boolean, keeperId?: number | null, consumerId?: number | null, sourceStore?: { __typename?: 'Store', id: number, name: string } | null, destinationStore?: { __typename?: 'Store', id: number, name: string } | null, items: Array<{ __typename?: 'Item', id: number, name: string, unit: string }>, transfers: Array<{ __typename?: 'Transfer', id: number, itemId: number, quantity: number }> }> };

export type GetTransfersQueryVariables = Exact<{ [key: string]: never; }>;


export type GetTransfersQuery = { __typename?: 'Query', getTransfers: Array<{ __typename?: 'Inventory', id: number, updatedAt: string, transferDate: string, granted: boolean, dispatched: boolean, received: boolean, keeperId?: number | null, consumerId?: number | null, sourceStore?: { __typename?: 'Store', id: number, name: string } | null, destinationStore?: { __typename?: 'Store', id: number, name: string } | null, items: Array<{ __typename?: 'Item', id: number, name: string, unit: string }>, transfers: Array<{ __typename?: 'Transfer', id: number, itemId: number, quantity: number }> }> };

export type GetItemBatchStocksQueryVariables = Exact<{
  itemId: Scalars['Float']['input'];
}>;


export type GetItemBatchStocksQuery = { __typename?: 'Query', getItemBatchStocks: Array<{ __typename?: 'BatchStock', id: number, batch: string, expireDate: string, stock: number, storeItemStocks?: Array<{ __typename?: 'StoreItemStock', id: number, storeId: number, stock: number }> | null }> };

export type GetBatchStockForStoreQueryVariables = Exact<{
  itemId?: InputMaybe<Scalars['Float']['input']>;
  storeId?: InputMaybe<Scalars['Float']['input']>;
}>;


export type GetBatchStockForStoreQuery = { __typename?: 'Query', getBatchStockForStore: Array<{ __typename?: 'BatchStock', itemId: number, batch: string, expireDate: string, stock: number, storeItemStocks?: Array<{ __typename?: 'StoreItemStock', storeId: number, stock: number }> | null }> };

export type GetItemStoreStocksPosQueryVariables = Exact<{
  itemId: Scalars['Float']['input'];
}>;


export type GetItemStoreStocksPosQuery = { __typename?: 'Query', getItemStoreStocks: Array<{ __typename?: 'StoreItemStock', id: number, storeId: number, stock: number, store: { __typename?: 'Store', id: number, name: string } }> };

export type GetItemStoreStocksQueryVariables = Exact<{
  itemId: Scalars['Float']['input'];
}>;


export type GetItemStoreStocksQuery = { __typename?: 'Query', getItemStoreStocks: Array<{ __typename?: 'StoreItemStock', id: number, storeId: number, stock: number, store: { __typename?: 'Store', id: number, name: string } }> };

export type GetItemBatchImportsQueryVariables = Exact<{
  itemId: Scalars['Float']['input'];
}>;


export type GetItemBatchImportsQuery = { __typename?: 'Query', getItemBatchImports: Array<{ __typename?: 'Import', id: number, importPrice: number, batch: string }> };

export type MeQueryVariables = Exact<{ [key: string]: never; }>;


export type MeQuery = { __typename?: 'Query', me?: { __typename?: 'User', id: number, firstname: string, middlename: string, lastname: string, email?: string | null, phone: string, image?: string | null, companyId: number, company: { __typename?: 'Company', id: number, name: string }, role: { __typename?: 'Role', id: number, name: string }, employee?: { __typename?: 'Employee', id: number, status: string, designation: string, licenceNumber: string, storeId?: number | null, role: { __typename?: 'Role', id: number, name: string }, department?: { __typename?: 'Department', id: number, name: string } | null, store?: { __typename?: 'Store', id: number, name: string } | null } | null, permissions?: Array<{ __typename?: 'Permission', id: number, name: string }> | null } | null };

export type GetPatientsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetPatientsQuery = { __typename?: 'Query', getPatients: Array<{ __typename?: 'Patient', id: number, firstname: string, lastname: string, middlename: string, phone: string, email?: string | null, status: string, dateOfBirth: string, nationalId?: string | null, religion: string, gender: string, fileNumber?: string | null, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceProvider: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, nextOfKinName: string, nextOfKinPhone: string, nextOfKinRelationship: string, address: { __typename?: 'Address', country: string, city: string, street: string, ward: string, district: string } }> };

export type GetPatientQueryVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type GetPatientQuery = { __typename?: 'Query', getPatient?: { __typename?: 'Patient', id: number, firstname: string, lastname: string, middlename: string, phone: string, email?: string | null, status: string, dateOfBirth: string, nationalId?: string | null, religion: string, gender: string, fileNumber?: string | null, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceProvider: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, nextOfKinName: string, nextOfKinPhone: string, nextOfKinRelationship: string, address: { __typename?: 'Address', country: string, city: string, street: string, ward: string, district: string } } | null };

export type GetVisitsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetVisitsQuery = { __typename?: 'Query', getVisits: Array<{ __typename?: 'Visit', id: number, status: string, type: string, reason: string, consultation?: string | null, currentLocation?: string | null, clientId: number, client: { __typename?: 'Patient', id: number, firstname: string, middlename: string, lastname: string, status: string, email?: string | null, phone: string, insuranceProvider: string, insuranceId?: string | null, insuranceUserId: string, insuranceStatus: string, insuranceSchemeId?: string | null, insuranceCardNumber?: string | null, gender: string, dateOfBirth: string, fileNumber?: string | null, nationalId?: string | null, bloodGroup?: string | null, registererId?: number | null }, bills: Array<{ __typename?: 'Bill', id: number, cleared: boolean, amount: number, paymentType: string }>, visitToClinics: Array<{ __typename?: 'VisitToClinic', id: number, clinicId: number }> }> };

export type GetPermissionsQueryVariables = Exact<{ [key: string]: never; }>;


export type GetPermissionsQuery = { __typename?: 'Query', getPermissions: Array<{ __typename?: 'Permission', id: number, name: string, roles?: Array<{ __typename?: 'Role', id: number, name: string }> | null }> };

export type GetFeaturesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetFeaturesQuery = { __typename?: 'Query', getFeatures: Array<{ __typename?: 'Feature', id: number, name: string, companies?: Array<{ __typename?: 'Company', id: number, name: string, type: string }> | null }> };

export type GetTypesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetTypesQuery = { __typename?: 'Query', getTypes: Array<{ __typename?: 'Type', id: number, name: string, description: string, category?: Array<{ __typename?: 'Category', id: number, name: string }> | null }> };

export type GetTypeQueryVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type GetTypeQuery = { __typename?: 'Query', getType?: { __typename?: 'Type', id: number, name: string, description: string, category?: Array<{ __typename?: 'Category', id: number, name: string }> | null } | null };

export type GetRolesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetRolesQuery = { __typename?: 'Query', getRoles: Array<{ __typename?: 'Role', id: number, sys: boolean, name: string }> };

export type GetRoleQueryVariables = Exact<{
  name: Scalars['String']['input'];
}>;


export type GetRoleQuery = { __typename?: 'Query', getRole?: { __typename?: 'Role', id: number, name: string, permissions?: Array<{ __typename?: 'Permission', id: number, name: string }> | null } | null };

export type GetAllCategoriesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetAllCategoriesQuery = { __typename?: 'Query', getAllCategories: Array<{ __typename?: 'Category', id: number, name: string }> };

export type GetCategoriesQueryVariables = Exact<{
  type: Scalars['String']['input'];
}>;


export type GetCategoriesQuery = { __typename?: 'Query', getCategories: Array<{ __typename?: 'Category', id: number, name: string }> };

export type ExpensesQueryVariables = Exact<{
  filter?: InputMaybe<ExpenseFilterInput>;
}>;


export type ExpensesQuery = { __typename?: 'Query', expenses: Array<{ __typename?: 'Expense', id: number, expenseDate: string, assetType: string, type: string, status: string, title: string, details: string, amount: number, createdAt: string, updatedAt: string, authorizer?: { __typename?: 'Employee', id: number } | null, requester?: { __typename?: 'Employee', id: number } | null }> };

export type GetExpensesQueryVariables = Exact<{ [key: string]: never; }>;


export type GetExpensesQuery = { __typename?: 'Query', getExpenses: Array<{ __typename?: 'Expense', id: number, expenseDate: string, assetType: string, type: string, status: string, title: string, details: string, amount: number, createdAt: string, updatedAt: string, authorizer?: { __typename?: 'Employee', id: number } | null, requester?: { __typename?: 'Employee', id: number } | null }> };

export type GetExpenseQueryVariables = Exact<{
  id: Scalars['Float']['input'];
}>;


export type GetExpenseQuery = { __typename?: 'Query', getExpense?: { __typename?: 'Expense', id: number, expenseDate: string, assetType: string, type: string, status: string, title: string, details: string, amount: number, createdAt: string, updatedAt: string, authorizer?: { __typename?: 'Employee', id: number } | null, requester?: { __typename?: 'Employee', id: number } | null } | null };

export type GetLatestPaymentQueryVariables = Exact<{ [key: string]: never; }>;


export type GetLatestPaymentQuery = { __typename?: 'Query', getLatestPayment?: { __typename?: 'Payment', id: number, status: PaymentStatus, startDate: string, endDate: string, packageName: string, billingCycle: BillingCycle, amount: number } | null };

export type GetActivePaymentQueryVariables = Exact<{
  companyId: Scalars['Float']['input'];
}>;


export type GetActivePaymentQuery = { __typename?: 'Query', getActivePayment?: { __typename?: 'Payment', id: number, status: PaymentStatus, startDate: string, endDate: string, packageName: string, billingCycle: BillingCycle, amount: number } | null };

export type PaymentsQueryVariables = Exact<{ [key: string]: never; }>;


export type PaymentsQuery = { __typename?: 'Query', payments: Array<{ __typename?: 'Payment', id: number, status: PaymentStatus, startDate: string, endDate: string, packageName: string, billingCycle: BillingCycle, amount: number }> };

export const ErrorFragmentDoc = gql`
    fragment Error on FieldError {
  target
  message
}
    `;
export const MeFragmentDoc = gql`
    fragment Me on User {
  id
  firstname
  middlename
  lastname
  email
  phone
  image
  companyId
  company {
    id
    name
  }
  role {
    id
    name
  }
  employee {
    id
    status
    designation
    department {
      id
      name
    }
  }
  permissions {
    id
    name
  }
}
    `;
export const BooleanResponseFragmentDoc = gql`
    fragment BooleanResponse on BooleanResponse {
  status
  error {
    target
    message
  }
}
    `;
export const ClientFragmentDoc = gql`
    fragment Client on Patient {
  id
  firstname
  lastname
  middlename
  phone
  email
  status
  dateOfBirth
  nationalId
  address {
    country
    city
    street
    ward
    district
  }
  religion
  gender
  fileNumber
  insuranceId
  insuranceUserId
  insuranceStatus
  insuranceProvider
  insuranceSchemeId
  insuranceCardNumber
  nextOfKinName
  nextOfKinPhone
  nextOfKinRelationship
}
    `;
export const RegisterCompanyDocument = gql`
    mutation registerCompany($params: RegisterCompanyArgs!) {
  registerCompany(params: $params) {
    id
    status
    error {
      target
      message
    }
  }
}
    `;
export type RegisterCompanyMutationFn = Apollo.MutationFunction<RegisterCompanyMutation, RegisterCompanyMutationVariables>;

/**
 * __useRegisterCompanyMutation__
 *
 * To run a mutation, you first call `useRegisterCompanyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRegisterCompanyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [registerCompanyMutation, { data, loading, error }] = useRegisterCompanyMutation({
 *   variables: {
 *      params: // value for 'params'
 *   },
 * });
 */
export function useRegisterCompanyMutation(baseOptions?: Apollo.MutationHookOptions<RegisterCompanyMutation, RegisterCompanyMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<RegisterCompanyMutation, RegisterCompanyMutationVariables>(RegisterCompanyDocument, options);
      }
export type RegisterCompanyMutationHookResult = ReturnType<typeof useRegisterCompanyMutation>;
export type RegisterCompanyMutationResult = Apollo.MutationResult<RegisterCompanyMutation>;
export type RegisterCompanyMutationOptions = Apollo.BaseMutationOptions<RegisterCompanyMutation, RegisterCompanyMutationVariables>;
export const AddStoreDocument = gql`
    mutation addStore($args: StoreInput!) {
  addStore(args: $args) {
    status
    error {
      target
      message
    }
  }
}
    `;
export type AddStoreMutationFn = Apollo.MutationFunction<AddStoreMutation, AddStoreMutationVariables>;

/**
 * __useAddStoreMutation__
 *
 * To run a mutation, you first call `useAddStoreMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddStoreMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addStoreMutation, { data, loading, error }] = useAddStoreMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useAddStoreMutation(baseOptions?: Apollo.MutationHookOptions<AddStoreMutation, AddStoreMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddStoreMutation, AddStoreMutationVariables>(AddStoreDocument, options);
      }
export type AddStoreMutationHookResult = ReturnType<typeof useAddStoreMutation>;
export type AddStoreMutationResult = Apollo.MutationResult<AddStoreMutation>;
export type AddStoreMutationOptions = Apollo.BaseMutationOptions<AddStoreMutation, AddStoreMutationVariables>;
export const EditStoreDocument = gql`
    mutation editStore($args: StoreEditInput!) {
  editStore(args: $args) {
    status
    error {
      target
      message
    }
  }
}
    `;
export type EditStoreMutationFn = Apollo.MutationFunction<EditStoreMutation, EditStoreMutationVariables>;

/**
 * __useEditStoreMutation__
 *
 * To run a mutation, you first call `useEditStoreMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditStoreMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editStoreMutation, { data, loading, error }] = useEditStoreMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useEditStoreMutation(baseOptions?: Apollo.MutationHookOptions<EditStoreMutation, EditStoreMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditStoreMutation, EditStoreMutationVariables>(EditStoreDocument, options);
      }
export type EditStoreMutationHookResult = ReturnType<typeof useEditStoreMutation>;
export type EditStoreMutationResult = Apollo.MutationResult<EditStoreMutation>;
export type EditStoreMutationOptions = Apollo.BaseMutationOptions<EditStoreMutation, EditStoreMutationVariables>;
export const AddSchedulesDocument = gql`
    mutation addSchedules($args: ScheduleBulkArgs!) {
  addSchedules(args: $args) {
    status
    error {
      target
      message
    }
    schedules {
      id
      day
      onTime
      offTime
      description
      clinicId
      employeeId
    }
  }
}
    `;
export type AddSchedulesMutationFn = Apollo.MutationFunction<AddSchedulesMutation, AddSchedulesMutationVariables>;

/**
 * __useAddSchedulesMutation__
 *
 * To run a mutation, you first call `useAddSchedulesMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddSchedulesMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addSchedulesMutation, { data, loading, error }] = useAddSchedulesMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useAddSchedulesMutation(baseOptions?: Apollo.MutationHookOptions<AddSchedulesMutation, AddSchedulesMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddSchedulesMutation, AddSchedulesMutationVariables>(AddSchedulesDocument, options);
      }
export type AddSchedulesMutationHookResult = ReturnType<typeof useAddSchedulesMutation>;
export type AddSchedulesMutationResult = Apollo.MutationResult<AddSchedulesMutation>;
export type AddSchedulesMutationOptions = Apollo.BaseMutationOptions<AddSchedulesMutation, AddSchedulesMutationVariables>;
export const EditSchedulesDocument = gql`
    mutation editSchedules($args: ScheduleEditBulkArgs!) {
  editSchedules(args: $args) {
    status
    error {
      target
      message
    }
    schedules {
      id
      day
      onTime
      offTime
      description
      clinicId
      employeeId
    }
  }
}
    `;
export type EditSchedulesMutationFn = Apollo.MutationFunction<EditSchedulesMutation, EditSchedulesMutationVariables>;

/**
 * __useEditSchedulesMutation__
 *
 * To run a mutation, you first call `useEditSchedulesMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditSchedulesMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editSchedulesMutation, { data, loading, error }] = useEditSchedulesMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useEditSchedulesMutation(baseOptions?: Apollo.MutationHookOptions<EditSchedulesMutation, EditSchedulesMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditSchedulesMutation, EditSchedulesMutationVariables>(EditSchedulesDocument, options);
      }
export type EditSchedulesMutationHookResult = ReturnType<typeof useEditSchedulesMutation>;
export type EditSchedulesMutationResult = Apollo.MutationResult<EditSchedulesMutation>;
export type EditSchedulesMutationOptions = Apollo.BaseMutationOptions<EditSchedulesMutation, EditSchedulesMutationVariables>;
export const AddDepartmentDocument = gql`
    mutation addDepartment($params: DepartmentInputArgs!) {
  addDepartment(params: $params) {
    error {
      target
      message
    }
    status
  }
}
    `;
export type AddDepartmentMutationFn = Apollo.MutationFunction<AddDepartmentMutation, AddDepartmentMutationVariables>;

/**
 * __useAddDepartmentMutation__
 *
 * To run a mutation, you first call `useAddDepartmentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddDepartmentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addDepartmentMutation, { data, loading, error }] = useAddDepartmentMutation({
 *   variables: {
 *      params: // value for 'params'
 *   },
 * });
 */
export function useAddDepartmentMutation(baseOptions?: Apollo.MutationHookOptions<AddDepartmentMutation, AddDepartmentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddDepartmentMutation, AddDepartmentMutationVariables>(AddDepartmentDocument, options);
      }
export type AddDepartmentMutationHookResult = ReturnType<typeof useAddDepartmentMutation>;
export type AddDepartmentMutationResult = Apollo.MutationResult<AddDepartmentMutation>;
export type AddDepartmentMutationOptions = Apollo.BaseMutationOptions<AddDepartmentMutation, AddDepartmentMutationVariables>;
export const EditDepartmentDocument = gql`
    mutation editDepartment($id: Float!, $params: DepartmentInputArgs!) {
  editDepartment(id: $id, params: $params) {
    error {
      target
      message
    }
    status
  }
}
    `;
export type EditDepartmentMutationFn = Apollo.MutationFunction<EditDepartmentMutation, EditDepartmentMutationVariables>;

/**
 * __useEditDepartmentMutation__
 *
 * To run a mutation, you first call `useEditDepartmentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditDepartmentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editDepartmentMutation, { data, loading, error }] = useEditDepartmentMutation({
 *   variables: {
 *      id: // value for 'id'
 *      params: // value for 'params'
 *   },
 * });
 */
export function useEditDepartmentMutation(baseOptions?: Apollo.MutationHookOptions<EditDepartmentMutation, EditDepartmentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditDepartmentMutation, EditDepartmentMutationVariables>(EditDepartmentDocument, options);
      }
export type EditDepartmentMutationHookResult = ReturnType<typeof useEditDepartmentMutation>;
export type EditDepartmentMutationResult = Apollo.MutationResult<EditDepartmentMutation>;
export type EditDepartmentMutationOptions = Apollo.BaseMutationOptions<EditDepartmentMutation, EditDepartmentMutationVariables>;
export const AddClinicDocument = gql`
    mutation addClinic($params: ClinicInputArgs!) {
  addClinic(params: $params) {
    error {
      target
      message
    }
    status
    clinic {
      id
      name
      description
      status
      clinicType
      size
    }
  }
}
    `;
export type AddClinicMutationFn = Apollo.MutationFunction<AddClinicMutation, AddClinicMutationVariables>;

/**
 * __useAddClinicMutation__
 *
 * To run a mutation, you first call `useAddClinicMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddClinicMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addClinicMutation, { data, loading, error }] = useAddClinicMutation({
 *   variables: {
 *      params: // value for 'params'
 *   },
 * });
 */
export function useAddClinicMutation(baseOptions?: Apollo.MutationHookOptions<AddClinicMutation, AddClinicMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddClinicMutation, AddClinicMutationVariables>(AddClinicDocument, options);
      }
export type AddClinicMutationHookResult = ReturnType<typeof useAddClinicMutation>;
export type AddClinicMutationResult = Apollo.MutationResult<AddClinicMutation>;
export type AddClinicMutationOptions = Apollo.BaseMutationOptions<AddClinicMutation, AddClinicMutationVariables>;
export const EditClinicDocument = gql`
    mutation editClinic($id: Int!, $params: ClinicEditArgs!) {
  editClinic(id: $id, params: $params) {
    error {
      target
      message
    }
    status
    clinic {
      id
      name
      description
      status
      clinicType
      size
    }
  }
}
    `;
export type EditClinicMutationFn = Apollo.MutationFunction<EditClinicMutation, EditClinicMutationVariables>;

/**
 * __useEditClinicMutation__
 *
 * To run a mutation, you first call `useEditClinicMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditClinicMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editClinicMutation, { data, loading, error }] = useEditClinicMutation({
 *   variables: {
 *      id: // value for 'id'
 *      params: // value for 'params'
 *   },
 * });
 */
export function useEditClinicMutation(baseOptions?: Apollo.MutationHookOptions<EditClinicMutation, EditClinicMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditClinicMutation, EditClinicMutationVariables>(EditClinicDocument, options);
      }
export type EditClinicMutationHookResult = ReturnType<typeof useEditClinicMutation>;
export type EditClinicMutationResult = Apollo.MutationResult<EditClinicMutation>;
export type EditClinicMutationOptions = Apollo.BaseMutationOptions<EditClinicMutation, EditClinicMutationVariables>;
export const DeleteClinicDocument = gql`
    mutation deleteClinic($id: Int!) {
  deleteClinic(id: $id) {
    error {
      target
      message
    }
    status
  }
}
    `;
export type DeleteClinicMutationFn = Apollo.MutationFunction<DeleteClinicMutation, DeleteClinicMutationVariables>;

/**
 * __useDeleteClinicMutation__
 *
 * To run a mutation, you first call `useDeleteClinicMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteClinicMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteClinicMutation, { data, loading, error }] = useDeleteClinicMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDeleteClinicMutation(baseOptions?: Apollo.MutationHookOptions<DeleteClinicMutation, DeleteClinicMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteClinicMutation, DeleteClinicMutationVariables>(DeleteClinicDocument, options);
      }
export type DeleteClinicMutationHookResult = ReturnType<typeof useDeleteClinicMutation>;
export type DeleteClinicMutationResult = Apollo.MutationResult<DeleteClinicMutation>;
export type DeleteClinicMutationOptions = Apollo.BaseMutationOptions<DeleteClinicMutation, DeleteClinicMutationVariables>;
export const SetHeadOfDepartmentDocument = gql`
    mutation SetHeadOfDepartment($departmentId: Int!, $employeeId: Int!) {
  setHeadOfDepartment(departmentId: $departmentId, employeeId: $employeeId) {
    status
    error {
      target
      message
    }
  }
}
    `;
export type SetHeadOfDepartmentMutationFn = Apollo.MutationFunction<SetHeadOfDepartmentMutation, SetHeadOfDepartmentMutationVariables>;

/**
 * __useSetHeadOfDepartmentMutation__
 *
 * To run a mutation, you first call `useSetHeadOfDepartmentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSetHeadOfDepartmentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [setHeadOfDepartmentMutation, { data, loading, error }] = useSetHeadOfDepartmentMutation({
 *   variables: {
 *      departmentId: // value for 'departmentId'
 *      employeeId: // value for 'employeeId'
 *   },
 * });
 */
export function useSetHeadOfDepartmentMutation(baseOptions?: Apollo.MutationHookOptions<SetHeadOfDepartmentMutation, SetHeadOfDepartmentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<SetHeadOfDepartmentMutation, SetHeadOfDepartmentMutationVariables>(SetHeadOfDepartmentDocument, options);
      }
export type SetHeadOfDepartmentMutationHookResult = ReturnType<typeof useSetHeadOfDepartmentMutation>;
export type SetHeadOfDepartmentMutationResult = Apollo.MutationResult<SetHeadOfDepartmentMutation>;
export type SetHeadOfDepartmentMutationOptions = Apollo.BaseMutationOptions<SetHeadOfDepartmentMutation, SetHeadOfDepartmentMutationVariables>;
export const RegisterEmployeeDocument = gql`
    mutation registerEmployee($params: RegisterEmployeeArgs!) {
  registerEmployee(params: $params) {
    status
    error {
      ...Error
    }
  }
}
    ${ErrorFragmentDoc}`;
export type RegisterEmployeeMutationFn = Apollo.MutationFunction<RegisterEmployeeMutation, RegisterEmployeeMutationVariables>;

/**
 * __useRegisterEmployeeMutation__
 *
 * To run a mutation, you first call `useRegisterEmployeeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRegisterEmployeeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [registerEmployeeMutation, { data, loading, error }] = useRegisterEmployeeMutation({
 *   variables: {
 *      params: // value for 'params'
 *   },
 * });
 */
export function useRegisterEmployeeMutation(baseOptions?: Apollo.MutationHookOptions<RegisterEmployeeMutation, RegisterEmployeeMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<RegisterEmployeeMutation, RegisterEmployeeMutationVariables>(RegisterEmployeeDocument, options);
      }
export type RegisterEmployeeMutationHookResult = ReturnType<typeof useRegisterEmployeeMutation>;
export type RegisterEmployeeMutationResult = Apollo.MutationResult<RegisterEmployeeMutation>;
export type RegisterEmployeeMutationOptions = Apollo.BaseMutationOptions<RegisterEmployeeMutation, RegisterEmployeeMutationVariables>;
export const ImportItemDocument = gql`
    mutation importItem($args: ImportInput!) {
  importItem(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ImportItemMutationFn = Apollo.MutationFunction<ImportItemMutation, ImportItemMutationVariables>;

/**
 * __useImportItemMutation__
 *
 * To run a mutation, you first call `useImportItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useImportItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [importItemMutation, { data, loading, error }] = useImportItemMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useImportItemMutation(baseOptions?: Apollo.MutationHookOptions<ImportItemMutation, ImportItemMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ImportItemMutation, ImportItemMutationVariables>(ImportItemDocument, options);
      }
export type ImportItemMutationHookResult = ReturnType<typeof useImportItemMutation>;
export type ImportItemMutationResult = Apollo.MutationResult<ImportItemMutation>;
export type ImportItemMutationOptions = Apollo.BaseMutationOptions<ImportItemMutation, ImportItemMutationVariables>;
export const AddItemDocument = gql`
    mutation addItem($args: ItemInput!) {
  addItem(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type AddItemMutationFn = Apollo.MutationFunction<AddItemMutation, AddItemMutationVariables>;

/**
 * __useAddItemMutation__
 *
 * To run a mutation, you first call `useAddItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addItemMutation, { data, loading, error }] = useAddItemMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useAddItemMutation(baseOptions?: Apollo.MutationHookOptions<AddItemMutation, AddItemMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddItemMutation, AddItemMutationVariables>(AddItemDocument, options);
      }
export type AddItemMutationHookResult = ReturnType<typeof useAddItemMutation>;
export type AddItemMutationResult = Apollo.MutationResult<AddItemMutation>;
export type AddItemMutationOptions = Apollo.BaseMutationOptions<AddItemMutation, AddItemMutationVariables>;
export const AddServiceDocument = gql`
    mutation addService($args: ServiceInput!) {
  addService(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type AddServiceMutationFn = Apollo.MutationFunction<AddServiceMutation, AddServiceMutationVariables>;

/**
 * __useAddServiceMutation__
 *
 * To run a mutation, you first call `useAddServiceMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddServiceMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addServiceMutation, { data, loading, error }] = useAddServiceMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useAddServiceMutation(baseOptions?: Apollo.MutationHookOptions<AddServiceMutation, AddServiceMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddServiceMutation, AddServiceMutationVariables>(AddServiceDocument, options);
      }
export type AddServiceMutationHookResult = ReturnType<typeof useAddServiceMutation>;
export type AddServiceMutationResult = Apollo.MutationResult<AddServiceMutation>;
export type AddServiceMutationOptions = Apollo.BaseMutationOptions<AddServiceMutation, AddServiceMutationVariables>;
export const WriteOffItemsDocument = gql`
    mutation writeOffItems($args: [WriteOffInput!]!) {
  writeOffItems(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type WriteOffItemsMutationFn = Apollo.MutationFunction<WriteOffItemsMutation, WriteOffItemsMutationVariables>;

/**
 * __useWriteOffItemsMutation__
 *
 * To run a mutation, you first call `useWriteOffItemsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useWriteOffItemsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [writeOffItemsMutation, { data, loading, error }] = useWriteOffItemsMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useWriteOffItemsMutation(baseOptions?: Apollo.MutationHookOptions<WriteOffItemsMutation, WriteOffItemsMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<WriteOffItemsMutation, WriteOffItemsMutationVariables>(WriteOffItemsDocument, options);
      }
export type WriteOffItemsMutationHookResult = ReturnType<typeof useWriteOffItemsMutation>;
export type WriteOffItemsMutationResult = Apollo.MutationResult<WriteOffItemsMutation>;
export type WriteOffItemsMutationOptions = Apollo.BaseMutationOptions<WriteOffItemsMutation, WriteOffItemsMutationVariables>;
export const DispatchItemsDocument = gql`
    mutation dispatchItems($args: [DispatchInput!]!) {
  dispatchItems(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type DispatchItemsMutationFn = Apollo.MutationFunction<DispatchItemsMutation, DispatchItemsMutationVariables>;

/**
 * __useDispatchItemsMutation__
 *
 * To run a mutation, you first call `useDispatchItemsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDispatchItemsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [dispatchItemsMutation, { data, loading, error }] = useDispatchItemsMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useDispatchItemsMutation(baseOptions?: Apollo.MutationHookOptions<DispatchItemsMutation, DispatchItemsMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DispatchItemsMutation, DispatchItemsMutationVariables>(DispatchItemsDocument, options);
      }
export type DispatchItemsMutationHookResult = ReturnType<typeof useDispatchItemsMutation>;
export type DispatchItemsMutationResult = Apollo.MutationResult<DispatchItemsMutation>;
export type DispatchItemsMutationOptions = Apollo.BaseMutationOptions<DispatchItemsMutation, DispatchItemsMutationVariables>;
export const InstantTransferDocument = gql`
    mutation instantTransfer($args: [TransferInput!]!, $sourceStore: Float!, $destinationStore: Float!) {
  instantTransfer(
    args: $args
    sourceStore: $sourceStore
    destinationStore: $destinationStore
  ) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type InstantTransferMutationFn = Apollo.MutationFunction<InstantTransferMutation, InstantTransferMutationVariables>;

/**
 * __useInstantTransferMutation__
 *
 * To run a mutation, you first call `useInstantTransferMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useInstantTransferMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [instantTransferMutation, { data, loading, error }] = useInstantTransferMutation({
 *   variables: {
 *      args: // value for 'args'
 *      sourceStore: // value for 'sourceStore'
 *      destinationStore: // value for 'destinationStore'
 *   },
 * });
 */
export function useInstantTransferMutation(baseOptions?: Apollo.MutationHookOptions<InstantTransferMutation, InstantTransferMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<InstantTransferMutation, InstantTransferMutationVariables>(InstantTransferDocument, options);
      }
export type InstantTransferMutationHookResult = ReturnType<typeof useInstantTransferMutation>;
export type InstantTransferMutationResult = Apollo.MutationResult<InstantTransferMutation>;
export type InstantTransferMutationOptions = Apollo.BaseMutationOptions<InstantTransferMutation, InstantTransferMutationVariables>;
export const QuickSaleDocument = gql`
    mutation quickSale($args: [SaleInput!]!) {
  quickSale(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type QuickSaleMutationFn = Apollo.MutationFunction<QuickSaleMutation, QuickSaleMutationVariables>;

/**
 * __useQuickSaleMutation__
 *
 * To run a mutation, you first call `useQuickSaleMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useQuickSaleMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [quickSaleMutation, { data, loading, error }] = useQuickSaleMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useQuickSaleMutation(baseOptions?: Apollo.MutationHookOptions<QuickSaleMutation, QuickSaleMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<QuickSaleMutation, QuickSaleMutationVariables>(QuickSaleDocument, options);
      }
export type QuickSaleMutationHookResult = ReturnType<typeof useQuickSaleMutation>;
export type QuickSaleMutationResult = Apollo.MutationResult<QuickSaleMutation>;
export type QuickSaleMutationOptions = Apollo.BaseMutationOptions<QuickSaleMutation, QuickSaleMutationVariables>;
export const ServePayLaterDocument = gql`
    mutation servePayLater($args: [SaleInput!]!, $servedTo: Float, $customerTag: String) {
  servePayLater(args: $args, servedTo: $servedTo, customerTag: $customerTag) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ServePayLaterMutationFn = Apollo.MutationFunction<ServePayLaterMutation, ServePayLaterMutationVariables>;

/**
 * __useServePayLaterMutation__
 *
 * To run a mutation, you first call `useServePayLaterMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useServePayLaterMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [servePayLaterMutation, { data, loading, error }] = useServePayLaterMutation({
 *   variables: {
 *      args: // value for 'args'
 *      servedTo: // value for 'servedTo'
 *      customerTag: // value for 'customerTag'
 *   },
 * });
 */
export function useServePayLaterMutation(baseOptions?: Apollo.MutationHookOptions<ServePayLaterMutation, ServePayLaterMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ServePayLaterMutation, ServePayLaterMutationVariables>(ServePayLaterDocument, options);
      }
export type ServePayLaterMutationHookResult = ReturnType<typeof useServePayLaterMutation>;
export type ServePayLaterMutationResult = Apollo.MutationResult<ServePayLaterMutation>;
export type ServePayLaterMutationOptions = Apollo.BaseMutationOptions<ServePayLaterMutation, ServePayLaterMutationVariables>;
export const UpdateBillDocument = gql`
    mutation updateBill($inventoryId: Float!, $args: [SaleInput!]!) {
  updateBill(inventoryId: $inventoryId, args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type UpdateBillMutationFn = Apollo.MutationFunction<UpdateBillMutation, UpdateBillMutationVariables>;

/**
 * __useUpdateBillMutation__
 *
 * To run a mutation, you first call `useUpdateBillMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateBillMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateBillMutation, { data, loading, error }] = useUpdateBillMutation({
 *   variables: {
 *      inventoryId: // value for 'inventoryId'
 *      args: // value for 'args'
 *   },
 * });
 */
export function useUpdateBillMutation(baseOptions?: Apollo.MutationHookOptions<UpdateBillMutation, UpdateBillMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<UpdateBillMutation, UpdateBillMutationVariables>(UpdateBillDocument, options);
      }
export type UpdateBillMutationHookResult = ReturnType<typeof useUpdateBillMutation>;
export type UpdateBillMutationResult = Apollo.MutationResult<UpdateBillMutation>;
export type UpdateBillMutationOptions = Apollo.BaseMutationOptions<UpdateBillMutation, UpdateBillMutationVariables>;
export const EditBillItemDocument = gql`
    mutation editBillItem($inventoryId: Float!, $transferId: Float!, $newQuantity: Float!) {
  editBillItem(
    inventoryId: $inventoryId
    transferId: $transferId
    newQuantity: $newQuantity
  ) {
    status
    error {
      target
      message
    }
    transfer {
      id
      itemId
      inventoryId
      quantity
      details
      price
      dispatched
    }
  }
}
    `;
export type EditBillItemMutationFn = Apollo.MutationFunction<EditBillItemMutation, EditBillItemMutationVariables>;

/**
 * __useEditBillItemMutation__
 *
 * To run a mutation, you first call `useEditBillItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditBillItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editBillItemMutation, { data, loading, error }] = useEditBillItemMutation({
 *   variables: {
 *      inventoryId: // value for 'inventoryId'
 *      transferId: // value for 'transferId'
 *      newQuantity: // value for 'newQuantity'
 *   },
 * });
 */
export function useEditBillItemMutation(baseOptions?: Apollo.MutationHookOptions<EditBillItemMutation, EditBillItemMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditBillItemMutation, EditBillItemMutationVariables>(EditBillItemDocument, options);
      }
export type EditBillItemMutationHookResult = ReturnType<typeof useEditBillItemMutation>;
export type EditBillItemMutationResult = Apollo.MutationResult<EditBillItemMutation>;
export type EditBillItemMutationOptions = Apollo.BaseMutationOptions<EditBillItemMutation, EditBillItemMutationVariables>;
export const DeleteBillItemDocument = gql`
    mutation deleteBillItem($inventoryId: Float!, $transferId: Float!) {
  deleteBillItem(inventoryId: $inventoryId, transferId: $transferId) {
    status
    error {
      target
      message
    }
  }
}
    `;
export type DeleteBillItemMutationFn = Apollo.MutationFunction<DeleteBillItemMutation, DeleteBillItemMutationVariables>;

/**
 * __useDeleteBillItemMutation__
 *
 * To run a mutation, you first call `useDeleteBillItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteBillItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteBillItemMutation, { data, loading, error }] = useDeleteBillItemMutation({
 *   variables: {
 *      inventoryId: // value for 'inventoryId'
 *      transferId: // value for 'transferId'
 *   },
 * });
 */
export function useDeleteBillItemMutation(baseOptions?: Apollo.MutationHookOptions<DeleteBillItemMutation, DeleteBillItemMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteBillItemMutation, DeleteBillItemMutationVariables>(DeleteBillItemDocument, options);
      }
export type DeleteBillItemMutationHookResult = ReturnType<typeof useDeleteBillItemMutation>;
export type DeleteBillItemMutationResult = Apollo.MutationResult<DeleteBillItemMutation>;
export type DeleteBillItemMutationOptions = Apollo.BaseMutationOptions<DeleteBillItemMutation, DeleteBillItemMutationVariables>;
export const ClearBillDocument = gql`
    mutation clearBill($saleId: Float!) {
  clearBill(saleId: $saleId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ClearBillMutationFn = Apollo.MutationFunction<ClearBillMutation, ClearBillMutationVariables>;

/**
 * __useClearBillMutation__
 *
 * To run a mutation, you first call `useClearBillMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useClearBillMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [clearBillMutation, { data, loading, error }] = useClearBillMutation({
 *   variables: {
 *      saleId: // value for 'saleId'
 *   },
 * });
 */
export function useClearBillMutation(baseOptions?: Apollo.MutationHookOptions<ClearBillMutation, ClearBillMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ClearBillMutation, ClearBillMutationVariables>(ClearBillDocument, options);
      }
export type ClearBillMutationHookResult = ReturnType<typeof useClearBillMutation>;
export type ClearBillMutationResult = Apollo.MutationResult<ClearBillMutation>;
export type ClearBillMutationOptions = Apollo.BaseMutationOptions<ClearBillMutation, ClearBillMutationVariables>;
export const ServePendingOrderDocument = gql`
    mutation servePendingOrder($transferId: Float!) {
  servePendingOrder(transferId: $transferId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ServePendingOrderMutationFn = Apollo.MutationFunction<ServePendingOrderMutation, ServePendingOrderMutationVariables>;

/**
 * __useServePendingOrderMutation__
 *
 * To run a mutation, you first call `useServePendingOrderMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useServePendingOrderMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [servePendingOrderMutation, { data, loading, error }] = useServePendingOrderMutation({
 *   variables: {
 *      transferId: // value for 'transferId'
 *   },
 * });
 */
export function useServePendingOrderMutation(baseOptions?: Apollo.MutationHookOptions<ServePendingOrderMutation, ServePendingOrderMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ServePendingOrderMutation, ServePendingOrderMutationVariables>(ServePendingOrderDocument, options);
      }
export type ServePendingOrderMutationHookResult = ReturnType<typeof useServePendingOrderMutation>;
export type ServePendingOrderMutationResult = Apollo.MutationResult<ServePendingOrderMutation>;
export type ServePendingOrderMutationOptions = Apollo.BaseMutationOptions<ServePendingOrderMutation, ServePendingOrderMutationVariables>;
export const TransferItemsDocument = gql`
    mutation transferItems($args: [DispatchInput!]!) {
  transferItems(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type TransferItemsMutationFn = Apollo.MutationFunction<TransferItemsMutation, TransferItemsMutationVariables>;

/**
 * __useTransferItemsMutation__
 *
 * To run a mutation, you first call `useTransferItemsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useTransferItemsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [transferItemsMutation, { data, loading, error }] = useTransferItemsMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useTransferItemsMutation(baseOptions?: Apollo.MutationHookOptions<TransferItemsMutation, TransferItemsMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<TransferItemsMutation, TransferItemsMutationVariables>(TransferItemsDocument, options);
      }
export type TransferItemsMutationHookResult = ReturnType<typeof useTransferItemsMutation>;
export type TransferItemsMutationResult = Apollo.MutationResult<TransferItemsMutation>;
export type TransferItemsMutationOptions = Apollo.BaseMutationOptions<TransferItemsMutation, TransferItemsMutationVariables>;
export const AddItemsFromExcelDocument = gql`
    mutation addItemsFromExcel($args: BulkItemInput!) {
  addItemsFromExcel(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type AddItemsFromExcelMutationFn = Apollo.MutationFunction<AddItemsFromExcelMutation, AddItemsFromExcelMutationVariables>;

/**
 * __useAddItemsFromExcelMutation__
 *
 * To run a mutation, you first call `useAddItemsFromExcelMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddItemsFromExcelMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addItemsFromExcelMutation, { data, loading, error }] = useAddItemsFromExcelMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useAddItemsFromExcelMutation(baseOptions?: Apollo.MutationHookOptions<AddItemsFromExcelMutation, AddItemsFromExcelMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddItemsFromExcelMutation, AddItemsFromExcelMutationVariables>(AddItemsFromExcelDocument, options);
      }
export type AddItemsFromExcelMutationHookResult = ReturnType<typeof useAddItemsFromExcelMutation>;
export type AddItemsFromExcelMutationResult = Apollo.MutationResult<AddItemsFromExcelMutation>;
export type AddItemsFromExcelMutationOptions = Apollo.BaseMutationOptions<AddItemsFromExcelMutation, AddItemsFromExcelMutationVariables>;
export const EditItemDocument = gql`
    mutation editItem($args: ItemInput!, $id: Float!) {
  editItem(id: $id, args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type EditItemMutationFn = Apollo.MutationFunction<EditItemMutation, EditItemMutationVariables>;

/**
 * __useEditItemMutation__
 *
 * To run a mutation, you first call `useEditItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editItemMutation, { data, loading, error }] = useEditItemMutation({
 *   variables: {
 *      args: // value for 'args'
 *      id: // value for 'id'
 *   },
 * });
 */
export function useEditItemMutation(baseOptions?: Apollo.MutationHookOptions<EditItemMutation, EditItemMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditItemMutation, EditItemMutationVariables>(EditItemDocument, options);
      }
export type EditItemMutationHookResult = ReturnType<typeof useEditItemMutation>;
export type EditItemMutationResult = Apollo.MutationResult<EditItemMutation>;
export type EditItemMutationOptions = Apollo.BaseMutationOptions<EditItemMutation, EditItemMutationVariables>;
export const EditServiceDocument = gql`
    mutation editService($args: ServiceInput!, $id: Float!) {
  editService(id: $id, args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type EditServiceMutationFn = Apollo.MutationFunction<EditServiceMutation, EditServiceMutationVariables>;

/**
 * __useEditServiceMutation__
 *
 * To run a mutation, you first call `useEditServiceMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditServiceMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editServiceMutation, { data, loading, error }] = useEditServiceMutation({
 *   variables: {
 *      args: // value for 'args'
 *      id: // value for 'id'
 *   },
 * });
 */
export function useEditServiceMutation(baseOptions?: Apollo.MutationHookOptions<EditServiceMutation, EditServiceMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditServiceMutation, EditServiceMutationVariables>(EditServiceDocument, options);
      }
export type EditServiceMutationHookResult = ReturnType<typeof useEditServiceMutation>;
export type EditServiceMutationResult = Apollo.MutationResult<EditServiceMutation>;
export type EditServiceMutationOptions = Apollo.BaseMutationOptions<EditServiceMutation, EditServiceMutationVariables>;
export const DeleteItemDocument = gql`
    mutation deleteItem($id: Float!) {
  deleteItem(id: $id) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type DeleteItemMutationFn = Apollo.MutationFunction<DeleteItemMutation, DeleteItemMutationVariables>;

/**
 * __useDeleteItemMutation__
 *
 * To run a mutation, you first call `useDeleteItemMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteItemMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteItemMutation, { data, loading, error }] = useDeleteItemMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDeleteItemMutation(baseOptions?: Apollo.MutationHookOptions<DeleteItemMutation, DeleteItemMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteItemMutation, DeleteItemMutationVariables>(DeleteItemDocument, options);
      }
export type DeleteItemMutationHookResult = ReturnType<typeof useDeleteItemMutation>;
export type DeleteItemMutationResult = Apollo.MutationResult<DeleteItemMutation>;
export type DeleteItemMutationOptions = Apollo.BaseMutationOptions<DeleteItemMutation, DeleteItemMutationVariables>;
export const ChangeInventoryApprovalStatusDocument = gql`
    mutation changeInventoryApprovalStatus($inventoryId: Float!) {
  changeInventoryApprovalStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ChangeInventoryApprovalStatusMutationFn = Apollo.MutationFunction<ChangeInventoryApprovalStatusMutation, ChangeInventoryApprovalStatusMutationVariables>;

/**
 * __useChangeInventoryApprovalStatusMutation__
 *
 * To run a mutation, you first call `useChangeInventoryApprovalStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useChangeInventoryApprovalStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [changeInventoryApprovalStatusMutation, { data, loading, error }] = useChangeInventoryApprovalStatusMutation({
 *   variables: {
 *      inventoryId: // value for 'inventoryId'
 *   },
 * });
 */
export function useChangeInventoryApprovalStatusMutation(baseOptions?: Apollo.MutationHookOptions<ChangeInventoryApprovalStatusMutation, ChangeInventoryApprovalStatusMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ChangeInventoryApprovalStatusMutation, ChangeInventoryApprovalStatusMutationVariables>(ChangeInventoryApprovalStatusDocument, options);
      }
export type ChangeInventoryApprovalStatusMutationHookResult = ReturnType<typeof useChangeInventoryApprovalStatusMutation>;
export type ChangeInventoryApprovalStatusMutationResult = Apollo.MutationResult<ChangeInventoryApprovalStatusMutation>;
export type ChangeInventoryApprovalStatusMutationOptions = Apollo.BaseMutationOptions<ChangeInventoryApprovalStatusMutation, ChangeInventoryApprovalStatusMutationVariables>;
export const ClearServedBillDocument = gql`
    mutation clearServedBill($inventoryId: Float!) {
  clearServedBill(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ClearServedBillMutationFn = Apollo.MutationFunction<ClearServedBillMutation, ClearServedBillMutationVariables>;

/**
 * __useClearServedBillMutation__
 *
 * To run a mutation, you first call `useClearServedBillMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useClearServedBillMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [clearServedBillMutation, { data, loading, error }] = useClearServedBillMutation({
 *   variables: {
 *      inventoryId: // value for 'inventoryId'
 *   },
 * });
 */
export function useClearServedBillMutation(baseOptions?: Apollo.MutationHookOptions<ClearServedBillMutation, ClearServedBillMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ClearServedBillMutation, ClearServedBillMutationVariables>(ClearServedBillDocument, options);
      }
export type ClearServedBillMutationHookResult = ReturnType<typeof useClearServedBillMutation>;
export type ClearServedBillMutationResult = Apollo.MutationResult<ClearServedBillMutation>;
export type ClearServedBillMutationOptions = Apollo.BaseMutationOptions<ClearServedBillMutation, ClearServedBillMutationVariables>;
export const ChangeInventoryDispatchedStatusDocument = gql`
    mutation changeInventoryDispatchedStatus($inventoryId: Float!) {
  changeInventoryDispatchedStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ChangeInventoryDispatchedStatusMutationFn = Apollo.MutationFunction<ChangeInventoryDispatchedStatusMutation, ChangeInventoryDispatchedStatusMutationVariables>;

/**
 * __useChangeInventoryDispatchedStatusMutation__
 *
 * To run a mutation, you first call `useChangeInventoryDispatchedStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useChangeInventoryDispatchedStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [changeInventoryDispatchedStatusMutation, { data, loading, error }] = useChangeInventoryDispatchedStatusMutation({
 *   variables: {
 *      inventoryId: // value for 'inventoryId'
 *   },
 * });
 */
export function useChangeInventoryDispatchedStatusMutation(baseOptions?: Apollo.MutationHookOptions<ChangeInventoryDispatchedStatusMutation, ChangeInventoryDispatchedStatusMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ChangeInventoryDispatchedStatusMutation, ChangeInventoryDispatchedStatusMutationVariables>(ChangeInventoryDispatchedStatusDocument, options);
      }
export type ChangeInventoryDispatchedStatusMutationHookResult = ReturnType<typeof useChangeInventoryDispatchedStatusMutation>;
export type ChangeInventoryDispatchedStatusMutationResult = Apollo.MutationResult<ChangeInventoryDispatchedStatusMutation>;
export type ChangeInventoryDispatchedStatusMutationOptions = Apollo.BaseMutationOptions<ChangeInventoryDispatchedStatusMutation, ChangeInventoryDispatchedStatusMutationVariables>;
export const ChangeInventorySoldStatusDocument = gql`
    mutation changeInventorySoldStatus($inventoryId: Float!) {
  changeInventorySoldStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ChangeInventorySoldStatusMutationFn = Apollo.MutationFunction<ChangeInventorySoldStatusMutation, ChangeInventorySoldStatusMutationVariables>;

/**
 * __useChangeInventorySoldStatusMutation__
 *
 * To run a mutation, you first call `useChangeInventorySoldStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useChangeInventorySoldStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [changeInventorySoldStatusMutation, { data, loading, error }] = useChangeInventorySoldStatusMutation({
 *   variables: {
 *      inventoryId: // value for 'inventoryId'
 *   },
 * });
 */
export function useChangeInventorySoldStatusMutation(baseOptions?: Apollo.MutationHookOptions<ChangeInventorySoldStatusMutation, ChangeInventorySoldStatusMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ChangeInventorySoldStatusMutation, ChangeInventorySoldStatusMutationVariables>(ChangeInventorySoldStatusDocument, options);
      }
export type ChangeInventorySoldStatusMutationHookResult = ReturnType<typeof useChangeInventorySoldStatusMutation>;
export type ChangeInventorySoldStatusMutationResult = Apollo.MutationResult<ChangeInventorySoldStatusMutation>;
export type ChangeInventorySoldStatusMutationOptions = Apollo.BaseMutationOptions<ChangeInventorySoldStatusMutation, ChangeInventorySoldStatusMutationVariables>;
export const ChangeInventoryReceivedStatusDocument = gql`
    mutation changeInventoryReceivedStatus($inventoryId: Float!) {
  changeInventoryReceivedStatus(inventoryId: $inventoryId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ChangeInventoryReceivedStatusMutationFn = Apollo.MutationFunction<ChangeInventoryReceivedStatusMutation, ChangeInventoryReceivedStatusMutationVariables>;

/**
 * __useChangeInventoryReceivedStatusMutation__
 *
 * To run a mutation, you first call `useChangeInventoryReceivedStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useChangeInventoryReceivedStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [changeInventoryReceivedStatusMutation, { data, loading, error }] = useChangeInventoryReceivedStatusMutation({
 *   variables: {
 *      inventoryId: // value for 'inventoryId'
 *   },
 * });
 */
export function useChangeInventoryReceivedStatusMutation(baseOptions?: Apollo.MutationHookOptions<ChangeInventoryReceivedStatusMutation, ChangeInventoryReceivedStatusMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ChangeInventoryReceivedStatusMutation, ChangeInventoryReceivedStatusMutationVariables>(ChangeInventoryReceivedStatusDocument, options);
      }
export type ChangeInventoryReceivedStatusMutationHookResult = ReturnType<typeof useChangeInventoryReceivedStatusMutation>;
export type ChangeInventoryReceivedStatusMutationResult = Apollo.MutationResult<ChangeInventoryReceivedStatusMutation>;
export type ChangeInventoryReceivedStatusMutationOptions = Apollo.BaseMutationOptions<ChangeInventoryReceivedStatusMutation, ChangeInventoryReceivedStatusMutationVariables>;
export const RegisterPatientDocument = gql`
    mutation registerPatient($params: RegisterPatientArgs!) {
  registerPatient(params: $params) {
    error {
      target
      message
    }
    patient {
      ...Client
    }
  }
}
    ${ClientFragmentDoc}`;
export type RegisterPatientMutationFn = Apollo.MutationFunction<RegisterPatientMutation, RegisterPatientMutationVariables>;

/**
 * __useRegisterPatientMutation__
 *
 * To run a mutation, you first call `useRegisterPatientMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRegisterPatientMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [registerPatientMutation, { data, loading, error }] = useRegisterPatientMutation({
 *   variables: {
 *      params: // value for 'params'
 *   },
 * });
 */
export function useRegisterPatientMutation(baseOptions?: Apollo.MutationHookOptions<RegisterPatientMutation, RegisterPatientMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<RegisterPatientMutation, RegisterPatientMutationVariables>(RegisterPatientDocument, options);
      }
export type RegisterPatientMutationHookResult = ReturnType<typeof useRegisterPatientMutation>;
export type RegisterPatientMutationResult = Apollo.MutationResult<RegisterPatientMutation>;
export type RegisterPatientMutationOptions = Apollo.BaseMutationOptions<RegisterPatientMutation, RegisterPatientMutationVariables>;
export const EditPatientDocument = gql`
    mutation editPatient($id: Float!, $params: RegisterPatientArgs!) {
  editPatient(id: $id, params: $params) {
    error {
      target
      message
    }
    patient {
      ...Client
    }
  }
}
    ${ClientFragmentDoc}`;
export type EditPatientMutationFn = Apollo.MutationFunction<EditPatientMutation, EditPatientMutationVariables>;

/**
 * __useEditPatientMutation__
 *
 * To run a mutation, you first call `useEditPatientMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditPatientMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editPatientMutation, { data, loading, error }] = useEditPatientMutation({
 *   variables: {
 *      id: // value for 'id'
 *      params: // value for 'params'
 *   },
 * });
 */
export function useEditPatientMutation(baseOptions?: Apollo.MutationHookOptions<EditPatientMutation, EditPatientMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditPatientMutation, EditPatientMutationVariables>(EditPatientDocument, options);
      }
export type EditPatientMutationHookResult = ReturnType<typeof useEditPatientMutation>;
export type EditPatientMutationResult = Apollo.MutationResult<EditPatientMutation>;
export type EditPatientMutationOptions = Apollo.BaseMutationOptions<EditPatientMutation, EditPatientMutationVariables>;
export const AddVisitDocument = gql`
    mutation addVisit($params: VisitInputArgs!) {
  addVisit(params: $params) {
    error {
      target
      message
    }
    visit {
      id
      status
      type
      reason
      consultation
      currentLocation
      clientId
      client {
        id
        firstname
        middlename
        lastname
        status
        email
        phone
        insuranceProvider
        insuranceId
        insuranceUserId
        insuranceStatus
        insuranceSchemeId
        insuranceCardNumber
        gender
        dateOfBirth
        fileNumber
        nationalId
        bloodGroup
        registererId
      }
    }
  }
}
    `;
export type AddVisitMutationFn = Apollo.MutationFunction<AddVisitMutation, AddVisitMutationVariables>;

/**
 * __useAddVisitMutation__
 *
 * To run a mutation, you first call `useAddVisitMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddVisitMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addVisitMutation, { data, loading, error }] = useAddVisitMutation({
 *   variables: {
 *      params: // value for 'params'
 *   },
 * });
 */
export function useAddVisitMutation(baseOptions?: Apollo.MutationHookOptions<AddVisitMutation, AddVisitMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddVisitMutation, AddVisitMutationVariables>(AddVisitDocument, options);
      }
export type AddVisitMutationHookResult = ReturnType<typeof useAddVisitMutation>;
export type AddVisitMutationResult = Apollo.MutationResult<AddVisitMutation>;
export type AddVisitMutationOptions = Apollo.BaseMutationOptions<AddVisitMutation, AddVisitMutationVariables>;
export const AddVitalsDocument = gql`
    mutation addVitals($params: VitalsInputArgs!) {
  addVitals(params: $params) {
    error {
      target
      message
    }
    visit {
      id
      status
      type
      reason
      consultation
      currentLocation
      clientId
      client {
        id
        firstname
        middlename
        lastname
        status
        email
        phone
        insuranceProvider
        insuranceId
        insuranceUserId
        insuranceStatus
        insuranceSchemeId
        insuranceCardNumber
        gender
        dateOfBirth
        fileNumber
        nationalId
        bloodGroup
        registererId
      }
      vitals {
        id
        height
        weight
        pulseRate
        bodyTemperature
        respirationRate
        oxygenSaturation
        systolicPressure
        diastolicPressure
        bloodGlucose
      }
    }
  }
}
    `;
export type AddVitalsMutationFn = Apollo.MutationFunction<AddVitalsMutation, AddVitalsMutationVariables>;

/**
 * __useAddVitalsMutation__
 *
 * To run a mutation, you first call `useAddVitalsMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddVitalsMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addVitalsMutation, { data, loading, error }] = useAddVitalsMutation({
 *   variables: {
 *      params: // value for 'params'
 *   },
 * });
 */
export function useAddVitalsMutation(baseOptions?: Apollo.MutationHookOptions<AddVitalsMutation, AddVitalsMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddVitalsMutation, AddVitalsMutationVariables>(AddVitalsDocument, options);
      }
export type AddVitalsMutationHookResult = ReturnType<typeof useAddVitalsMutation>;
export type AddVitalsMutationResult = Apollo.MutationResult<AddVitalsMutation>;
export type AddVitalsMutationOptions = Apollo.BaseMutationOptions<AddVitalsMutation, AddVitalsMutationVariables>;
export const DeleteTypeDocument = gql`
    mutation deleteType($id: Float!) {
  deleteType(id: $id) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type DeleteTypeMutationFn = Apollo.MutationFunction<DeleteTypeMutation, DeleteTypeMutationVariables>;

/**
 * __useDeleteTypeMutation__
 *
 * To run a mutation, you first call `useDeleteTypeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteTypeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteTypeMutation, { data, loading, error }] = useDeleteTypeMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDeleteTypeMutation(baseOptions?: Apollo.MutationHookOptions<DeleteTypeMutation, DeleteTypeMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteTypeMutation, DeleteTypeMutationVariables>(DeleteTypeDocument, options);
      }
export type DeleteTypeMutationHookResult = ReturnType<typeof useDeleteTypeMutation>;
export type DeleteTypeMutationResult = Apollo.MutationResult<DeleteTypeMutation>;
export type DeleteTypeMutationOptions = Apollo.BaseMutationOptions<DeleteTypeMutation, DeleteTypeMutationVariables>;
export const AddTypeDocument = gql`
    mutation addType($args: TypeArgs!) {
  addType(args: $args) {
    status
    error {
      target
      message
    }
    data {
      id
      name
      description
    }
  }
}
    `;
export type AddTypeMutationFn = Apollo.MutationFunction<AddTypeMutation, AddTypeMutationVariables>;

/**
 * __useAddTypeMutation__
 *
 * To run a mutation, you first call `useAddTypeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddTypeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addTypeMutation, { data, loading, error }] = useAddTypeMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useAddTypeMutation(baseOptions?: Apollo.MutationHookOptions<AddTypeMutation, AddTypeMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddTypeMutation, AddTypeMutationVariables>(AddTypeDocument, options);
      }
export type AddTypeMutationHookResult = ReturnType<typeof useAddTypeMutation>;
export type AddTypeMutationResult = Apollo.MutationResult<AddTypeMutation>;
export type AddTypeMutationOptions = Apollo.BaseMutationOptions<AddTypeMutation, AddTypeMutationVariables>;
export const EditTypeDocument = gql`
    mutation editType($id: Float!, $args: TypeEditArgs!) {
  editType(id: $id, args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type EditTypeMutationFn = Apollo.MutationFunction<EditTypeMutation, EditTypeMutationVariables>;

/**
 * __useEditTypeMutation__
 *
 * To run a mutation, you first call `useEditTypeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditTypeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editTypeMutation, { data, loading, error }] = useEditTypeMutation({
 *   variables: {
 *      id: // value for 'id'
 *      args: // value for 'args'
 *   },
 * });
 */
export function useEditTypeMutation(baseOptions?: Apollo.MutationHookOptions<EditTypeMutation, EditTypeMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditTypeMutation, EditTypeMutationVariables>(EditTypeDocument, options);
      }
export type EditTypeMutationHookResult = ReturnType<typeof useEditTypeMutation>;
export type EditTypeMutationResult = Apollo.MutationResult<EditTypeMutation>;
export type EditTypeMutationOptions = Apollo.BaseMutationOptions<EditTypeMutation, EditTypeMutationVariables>;
export const AddPermissionDocument = gql`
    mutation addPermission($name: String!, $userId: Float, $roleId: Float) {
  addPermission(name: $name, userId: $userId, roleId: $roleId) {
    status
    error {
      target
      message
    }
    permission {
      id
      name
    }
  }
}
    `;
export type AddPermissionMutationFn = Apollo.MutationFunction<AddPermissionMutation, AddPermissionMutationVariables>;

/**
 * __useAddPermissionMutation__
 *
 * To run a mutation, you first call `useAddPermissionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddPermissionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addPermissionMutation, { data, loading, error }] = useAddPermissionMutation({
 *   variables: {
 *      name: // value for 'name'
 *      userId: // value for 'userId'
 *      roleId: // value for 'roleId'
 *   },
 * });
 */
export function useAddPermissionMutation(baseOptions?: Apollo.MutationHookOptions<AddPermissionMutation, AddPermissionMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddPermissionMutation, AddPermissionMutationVariables>(AddPermissionDocument, options);
      }
export type AddPermissionMutationHookResult = ReturnType<typeof useAddPermissionMutation>;
export type AddPermissionMutationResult = Apollo.MutationResult<AddPermissionMutation>;
export type AddPermissionMutationOptions = Apollo.BaseMutationOptions<AddPermissionMutation, AddPermissionMutationVariables>;
export const RemovePermissionDocument = gql`
    mutation removePermission($name: String!, $userId: Float, $roleId: Float) {
  removePermission(name: $name, userId: $userId, roleId: $roleId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type RemovePermissionMutationFn = Apollo.MutationFunction<RemovePermissionMutation, RemovePermissionMutationVariables>;

/**
 * __useRemovePermissionMutation__
 *
 * To run a mutation, you first call `useRemovePermissionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRemovePermissionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [removePermissionMutation, { data, loading, error }] = useRemovePermissionMutation({
 *   variables: {
 *      name: // value for 'name'
 *      userId: // value for 'userId'
 *      roleId: // value for 'roleId'
 *   },
 * });
 */
export function useRemovePermissionMutation(baseOptions?: Apollo.MutationHookOptions<RemovePermissionMutation, RemovePermissionMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<RemovePermissionMutation, RemovePermissionMutationVariables>(RemovePermissionDocument, options);
      }
export type RemovePermissionMutationHookResult = ReturnType<typeof useRemovePermissionMutation>;
export type RemovePermissionMutationResult = Apollo.MutationResult<RemovePermissionMutation>;
export type RemovePermissionMutationOptions = Apollo.BaseMutationOptions<RemovePermissionMutation, RemovePermissionMutationVariables>;
export const EditPermissionDocument = gql`
    mutation editPermission($id: Float!, $name: String!) {
  editPermission(id: $id, name: $name) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type EditPermissionMutationFn = Apollo.MutationFunction<EditPermissionMutation, EditPermissionMutationVariables>;

/**
 * __useEditPermissionMutation__
 *
 * To run a mutation, you first call `useEditPermissionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditPermissionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editPermissionMutation, { data, loading, error }] = useEditPermissionMutation({
 *   variables: {
 *      id: // value for 'id'
 *      name: // value for 'name'
 *   },
 * });
 */
export function useEditPermissionMutation(baseOptions?: Apollo.MutationHookOptions<EditPermissionMutation, EditPermissionMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditPermissionMutation, EditPermissionMutationVariables>(EditPermissionDocument, options);
      }
export type EditPermissionMutationHookResult = ReturnType<typeof useEditPermissionMutation>;
export type EditPermissionMutationResult = Apollo.MutationResult<EditPermissionMutation>;
export type EditPermissionMutationOptions = Apollo.BaseMutationOptions<EditPermissionMutation, EditPermissionMutationVariables>;
export const DeletePermissionDocument = gql`
    mutation deletePermission($id: Float!) {
  deletePermission(id: $id) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type DeletePermissionMutationFn = Apollo.MutationFunction<DeletePermissionMutation, DeletePermissionMutationVariables>;

/**
 * __useDeletePermissionMutation__
 *
 * To run a mutation, you first call `useDeletePermissionMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeletePermissionMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deletePermissionMutation, { data, loading, error }] = useDeletePermissionMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDeletePermissionMutation(baseOptions?: Apollo.MutationHookOptions<DeletePermissionMutation, DeletePermissionMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeletePermissionMutation, DeletePermissionMutationVariables>(DeletePermissionDocument, options);
      }
export type DeletePermissionMutationHookResult = ReturnType<typeof useDeletePermissionMutation>;
export type DeletePermissionMutationResult = Apollo.MutationResult<DeletePermissionMutation>;
export type DeletePermissionMutationOptions = Apollo.BaseMutationOptions<DeletePermissionMutation, DeletePermissionMutationVariables>;
export const AddFeatureDocument = gql`
    mutation addFeature($name: String!, $companyId: Float!) {
  addFeature(name: $name, companyId: $companyId) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type AddFeatureMutationFn = Apollo.MutationFunction<AddFeatureMutation, AddFeatureMutationVariables>;

/**
 * __useAddFeatureMutation__
 *
 * To run a mutation, you first call `useAddFeatureMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddFeatureMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addFeatureMutation, { data, loading, error }] = useAddFeatureMutation({
 *   variables: {
 *      name: // value for 'name'
 *      companyId: // value for 'companyId'
 *   },
 * });
 */
export function useAddFeatureMutation(baseOptions?: Apollo.MutationHookOptions<AddFeatureMutation, AddFeatureMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddFeatureMutation, AddFeatureMutationVariables>(AddFeatureDocument, options);
      }
export type AddFeatureMutationHookResult = ReturnType<typeof useAddFeatureMutation>;
export type AddFeatureMutationResult = Apollo.MutationResult<AddFeatureMutation>;
export type AddFeatureMutationOptions = Apollo.BaseMutationOptions<AddFeatureMutation, AddFeatureMutationVariables>;
export const EditFeatureDocument = gql`
    mutation editFeature($id: Float!, $name: String!) {
  editFeature(id: $id, name: $name) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type EditFeatureMutationFn = Apollo.MutationFunction<EditFeatureMutation, EditFeatureMutationVariables>;

/**
 * __useEditFeatureMutation__
 *
 * To run a mutation, you first call `useEditFeatureMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditFeatureMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editFeatureMutation, { data, loading, error }] = useEditFeatureMutation({
 *   variables: {
 *      id: // value for 'id'
 *      name: // value for 'name'
 *   },
 * });
 */
export function useEditFeatureMutation(baseOptions?: Apollo.MutationHookOptions<EditFeatureMutation, EditFeatureMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditFeatureMutation, EditFeatureMutationVariables>(EditFeatureDocument, options);
      }
export type EditFeatureMutationHookResult = ReturnType<typeof useEditFeatureMutation>;
export type EditFeatureMutationResult = Apollo.MutationResult<EditFeatureMutation>;
export type EditFeatureMutationOptions = Apollo.BaseMutationOptions<EditFeatureMutation, EditFeatureMutationVariables>;
export const DeleteFeatureDocument = gql`
    mutation deleteFeature($id: Float!) {
  deleteFeature(id: $id) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type DeleteFeatureMutationFn = Apollo.MutationFunction<DeleteFeatureMutation, DeleteFeatureMutationVariables>;

/**
 * __useDeleteFeatureMutation__
 *
 * To run a mutation, you first call `useDeleteFeatureMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteFeatureMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteFeatureMutation, { data, loading, error }] = useDeleteFeatureMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDeleteFeatureMutation(baseOptions?: Apollo.MutationHookOptions<DeleteFeatureMutation, DeleteFeatureMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteFeatureMutation, DeleteFeatureMutationVariables>(DeleteFeatureDocument, options);
      }
export type DeleteFeatureMutationHookResult = ReturnType<typeof useDeleteFeatureMutation>;
export type DeleteFeatureMutationResult = Apollo.MutationResult<DeleteFeatureMutation>;
export type DeleteFeatureMutationOptions = Apollo.BaseMutationOptions<DeleteFeatureMutation, DeleteFeatureMutationVariables>;
export const AddRoleDocument = gql`
    mutation addRole($name: String!) {
  addRole(name: $name) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type AddRoleMutationFn = Apollo.MutationFunction<AddRoleMutation, AddRoleMutationVariables>;

/**
 * __useAddRoleMutation__
 *
 * To run a mutation, you first call `useAddRoleMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddRoleMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addRoleMutation, { data, loading, error }] = useAddRoleMutation({
 *   variables: {
 *      name: // value for 'name'
 *   },
 * });
 */
export function useAddRoleMutation(baseOptions?: Apollo.MutationHookOptions<AddRoleMutation, AddRoleMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddRoleMutation, AddRoleMutationVariables>(AddRoleDocument, options);
      }
export type AddRoleMutationHookResult = ReturnType<typeof useAddRoleMutation>;
export type AddRoleMutationResult = Apollo.MutationResult<AddRoleMutation>;
export type AddRoleMutationOptions = Apollo.BaseMutationOptions<AddRoleMutation, AddRoleMutationVariables>;
export const EditRoleDocument = gql`
    mutation editRole($id: Float!, $args: RoleArgs!) {
  editRole(id: $id, args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type EditRoleMutationFn = Apollo.MutationFunction<EditRoleMutation, EditRoleMutationVariables>;

/**
 * __useEditRoleMutation__
 *
 * To run a mutation, you first call `useEditRoleMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditRoleMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editRoleMutation, { data, loading, error }] = useEditRoleMutation({
 *   variables: {
 *      id: // value for 'id'
 *      args: // value for 'args'
 *   },
 * });
 */
export function useEditRoleMutation(baseOptions?: Apollo.MutationHookOptions<EditRoleMutation, EditRoleMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditRoleMutation, EditRoleMutationVariables>(EditRoleDocument, options);
      }
export type EditRoleMutationHookResult = ReturnType<typeof useEditRoleMutation>;
export type EditRoleMutationResult = Apollo.MutationResult<EditRoleMutation>;
export type EditRoleMutationOptions = Apollo.BaseMutationOptions<EditRoleMutation, EditRoleMutationVariables>;
export const DeleteRoleDocument = gql`
    mutation deleteRole($id: Float!) {
  deleteRole(id: $id) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type DeleteRoleMutationFn = Apollo.MutationFunction<DeleteRoleMutation, DeleteRoleMutationVariables>;

/**
 * __useDeleteRoleMutation__
 *
 * To run a mutation, you first call `useDeleteRoleMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteRoleMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteRoleMutation, { data, loading, error }] = useDeleteRoleMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDeleteRoleMutation(baseOptions?: Apollo.MutationHookOptions<DeleteRoleMutation, DeleteRoleMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteRoleMutation, DeleteRoleMutationVariables>(DeleteRoleDocument, options);
      }
export type DeleteRoleMutationHookResult = ReturnType<typeof useDeleteRoleMutation>;
export type DeleteRoleMutationResult = Apollo.MutationResult<DeleteRoleMutation>;
export type DeleteRoleMutationOptions = Apollo.BaseMutationOptions<DeleteRoleMutation, DeleteRoleMutationVariables>;
export const AddCategoryDocument = gql`
    mutation addCategory($args: CategoryArgs!) {
  addCategory(args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type AddCategoryMutationFn = Apollo.MutationFunction<AddCategoryMutation, AddCategoryMutationVariables>;

/**
 * __useAddCategoryMutation__
 *
 * To run a mutation, you first call `useAddCategoryMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddCategoryMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addCategoryMutation, { data, loading, error }] = useAddCategoryMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useAddCategoryMutation(baseOptions?: Apollo.MutationHookOptions<AddCategoryMutation, AddCategoryMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddCategoryMutation, AddCategoryMutationVariables>(AddCategoryDocument, options);
      }
export type AddCategoryMutationHookResult = ReturnType<typeof useAddCategoryMutation>;
export type AddCategoryMutationResult = Apollo.MutationResult<AddCategoryMutation>;
export type AddCategoryMutationOptions = Apollo.BaseMutationOptions<AddCategoryMutation, AddCategoryMutationVariables>;
export const AddCategoryWithTypeNameDocument = gql`
    mutation addCategoryWithTypeName($args: CategoryTypeArgs!) {
  addCategoryWithTypeName(args: $args) {
    error {
      target
      message
    }
    category {
      id
      name
    }
  }
}
    `;
export type AddCategoryWithTypeNameMutationFn = Apollo.MutationFunction<AddCategoryWithTypeNameMutation, AddCategoryWithTypeNameMutationVariables>;

/**
 * __useAddCategoryWithTypeNameMutation__
 *
 * To run a mutation, you first call `useAddCategoryWithTypeNameMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddCategoryWithTypeNameMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addCategoryWithTypeNameMutation, { data, loading, error }] = useAddCategoryWithTypeNameMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useAddCategoryWithTypeNameMutation(baseOptions?: Apollo.MutationHookOptions<AddCategoryWithTypeNameMutation, AddCategoryWithTypeNameMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddCategoryWithTypeNameMutation, AddCategoryWithTypeNameMutationVariables>(AddCategoryWithTypeNameDocument, options);
      }
export type AddCategoryWithTypeNameMutationHookResult = ReturnType<typeof useAddCategoryWithTypeNameMutation>;
export type AddCategoryWithTypeNameMutationResult = Apollo.MutationResult<AddCategoryWithTypeNameMutation>;
export type AddCategoryWithTypeNameMutationOptions = Apollo.BaseMutationOptions<AddCategoryWithTypeNameMutation, AddCategoryWithTypeNameMutationVariables>;
export const EditCategoryDocument = gql`
    mutation editCategory($id: Float!, $args: CategoryArgs!) {
  editCategory(id: $id, args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type EditCategoryMutationFn = Apollo.MutationFunction<EditCategoryMutation, EditCategoryMutationVariables>;

/**
 * __useEditCategoryMutation__
 *
 * To run a mutation, you first call `useEditCategoryMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditCategoryMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editCategoryMutation, { data, loading, error }] = useEditCategoryMutation({
 *   variables: {
 *      id: // value for 'id'
 *      args: // value for 'args'
 *   },
 * });
 */
export function useEditCategoryMutation(baseOptions?: Apollo.MutationHookOptions<EditCategoryMutation, EditCategoryMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditCategoryMutation, EditCategoryMutationVariables>(EditCategoryDocument, options);
      }
export type EditCategoryMutationHookResult = ReturnType<typeof useEditCategoryMutation>;
export type EditCategoryMutationResult = Apollo.MutationResult<EditCategoryMutation>;
export type EditCategoryMutationOptions = Apollo.BaseMutationOptions<EditCategoryMutation, EditCategoryMutationVariables>;
export const EditCategoryByNameDocument = gql`
    mutation editCategoryByName($name: String!, $args: CategoryArgs!) {
  editCategoryByName(name: $name, args: $args) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type EditCategoryByNameMutationFn = Apollo.MutationFunction<EditCategoryByNameMutation, EditCategoryByNameMutationVariables>;

/**
 * __useEditCategoryByNameMutation__
 *
 * To run a mutation, you first call `useEditCategoryByNameMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditCategoryByNameMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editCategoryByNameMutation, { data, loading, error }] = useEditCategoryByNameMutation({
 *   variables: {
 *      name: // value for 'name'
 *      args: // value for 'args'
 *   },
 * });
 */
export function useEditCategoryByNameMutation(baseOptions?: Apollo.MutationHookOptions<EditCategoryByNameMutation, EditCategoryByNameMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditCategoryByNameMutation, EditCategoryByNameMutationVariables>(EditCategoryByNameDocument, options);
      }
export type EditCategoryByNameMutationHookResult = ReturnType<typeof useEditCategoryByNameMutation>;
export type EditCategoryByNameMutationResult = Apollo.MutationResult<EditCategoryByNameMutation>;
export type EditCategoryByNameMutationOptions = Apollo.BaseMutationOptions<EditCategoryByNameMutation, EditCategoryByNameMutationVariables>;
export const DeleteCategoryDocument = gql`
    mutation deleteCategory($id: Float!) {
  deleteCategory(id: $id) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type DeleteCategoryMutationFn = Apollo.MutationFunction<DeleteCategoryMutation, DeleteCategoryMutationVariables>;

/**
 * __useDeleteCategoryMutation__
 *
 * To run a mutation, you first call `useDeleteCategoryMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteCategoryMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteCategoryMutation, { data, loading, error }] = useDeleteCategoryMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDeleteCategoryMutation(baseOptions?: Apollo.MutationHookOptions<DeleteCategoryMutation, DeleteCategoryMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteCategoryMutation, DeleteCategoryMutationVariables>(DeleteCategoryDocument, options);
      }
export type DeleteCategoryMutationHookResult = ReturnType<typeof useDeleteCategoryMutation>;
export type DeleteCategoryMutationResult = Apollo.MutationResult<DeleteCategoryMutation>;
export type DeleteCategoryMutationOptions = Apollo.BaseMutationOptions<DeleteCategoryMutation, DeleteCategoryMutationVariables>;
export const AddExpenseDocument = gql`
    mutation addExpense($args: ExpenseInput!) {
  addExpense(args: $args) {
    status
    error {
      target
      message
    }
  }
}
    `;
export type AddExpenseMutationFn = Apollo.MutationFunction<AddExpenseMutation, AddExpenseMutationVariables>;

/**
 * __useAddExpenseMutation__
 *
 * To run a mutation, you first call `useAddExpenseMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddExpenseMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addExpenseMutation, { data, loading, error }] = useAddExpenseMutation({
 *   variables: {
 *      args: // value for 'args'
 *   },
 * });
 */
export function useAddExpenseMutation(baseOptions?: Apollo.MutationHookOptions<AddExpenseMutation, AddExpenseMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AddExpenseMutation, AddExpenseMutationVariables>(AddExpenseDocument, options);
      }
export type AddExpenseMutationHookResult = ReturnType<typeof useAddExpenseMutation>;
export type AddExpenseMutationResult = Apollo.MutationResult<AddExpenseMutation>;
export type AddExpenseMutationOptions = Apollo.BaseMutationOptions<AddExpenseMutation, AddExpenseMutationVariables>;
export const EditExpenseDocument = gql`
    mutation editExpense($id: Float!, $args: ExpenseInput!) {
  editExpense(id: $id, args: $args) {
    status
    error {
      target
      message
    }
  }
}
    `;
export type EditExpenseMutationFn = Apollo.MutationFunction<EditExpenseMutation, EditExpenseMutationVariables>;

/**
 * __useEditExpenseMutation__
 *
 * To run a mutation, you first call `useEditExpenseMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditExpenseMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editExpenseMutation, { data, loading, error }] = useEditExpenseMutation({
 *   variables: {
 *      id: // value for 'id'
 *      args: // value for 'args'
 *   },
 * });
 */
export function useEditExpenseMutation(baseOptions?: Apollo.MutationHookOptions<EditExpenseMutation, EditExpenseMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditExpenseMutation, EditExpenseMutationVariables>(EditExpenseDocument, options);
      }
export type EditExpenseMutationHookResult = ReturnType<typeof useEditExpenseMutation>;
export type EditExpenseMutationResult = Apollo.MutationResult<EditExpenseMutation>;
export type EditExpenseMutationOptions = Apollo.BaseMutationOptions<EditExpenseMutation, EditExpenseMutationVariables>;
export const AuthorizeExpenseDocument = gql`
    mutation authorizeExpense($id: Float!) {
  authorizeExpense(id: $id) {
    status
    error {
      target
      message
    }
  }
}
    `;
export type AuthorizeExpenseMutationFn = Apollo.MutationFunction<AuthorizeExpenseMutation, AuthorizeExpenseMutationVariables>;

/**
 * __useAuthorizeExpenseMutation__
 *
 * To run a mutation, you first call `useAuthorizeExpenseMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAuthorizeExpenseMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [authorizeExpenseMutation, { data, loading, error }] = useAuthorizeExpenseMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useAuthorizeExpenseMutation(baseOptions?: Apollo.MutationHookOptions<AuthorizeExpenseMutation, AuthorizeExpenseMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<AuthorizeExpenseMutation, AuthorizeExpenseMutationVariables>(AuthorizeExpenseDocument, options);
      }
export type AuthorizeExpenseMutationHookResult = ReturnType<typeof useAuthorizeExpenseMutation>;
export type AuthorizeExpenseMutationResult = Apollo.MutationResult<AuthorizeExpenseMutation>;
export type AuthorizeExpenseMutationOptions = Apollo.BaseMutationOptions<AuthorizeExpenseMutation, AuthorizeExpenseMutationVariables>;
export const DeleteExpenseDocument = gql`
    mutation deleteExpense($id: Float!) {
  deleteExpense(id: $id) {
    status
    error {
      target
      message
    }
  }
}
    `;
export type DeleteExpenseMutationFn = Apollo.MutationFunction<DeleteExpenseMutation, DeleteExpenseMutationVariables>;

/**
 * __useDeleteExpenseMutation__
 *
 * To run a mutation, you first call `useDeleteExpenseMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteExpenseMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteExpenseMutation, { data, loading, error }] = useDeleteExpenseMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDeleteExpenseMutation(baseOptions?: Apollo.MutationHookOptions<DeleteExpenseMutation, DeleteExpenseMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<DeleteExpenseMutation, DeleteExpenseMutationVariables>(DeleteExpenseDocument, options);
      }
export type DeleteExpenseMutationHookResult = ReturnType<typeof useDeleteExpenseMutation>;
export type DeleteExpenseMutationResult = Apollo.MutationResult<DeleteExpenseMutation>;
export type DeleteExpenseMutationOptions = Apollo.BaseMutationOptions<DeleteExpenseMutation, DeleteExpenseMutationVariables>;
export const CreatePaymentDocument = gql`
    mutation createPayment($input: CreatePaymentInput!) {
  createPayment(input: $input) {
    id
    status
    packageName
    startDate
    endDate
  }
}
    `;
export type CreatePaymentMutationFn = Apollo.MutationFunction<CreatePaymentMutation, CreatePaymentMutationVariables>;

/**
 * __useCreatePaymentMutation__
 *
 * To run a mutation, you first call `useCreatePaymentMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreatePaymentMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createPaymentMutation, { data, loading, error }] = useCreatePaymentMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreatePaymentMutation(baseOptions?: Apollo.MutationHookOptions<CreatePaymentMutation, CreatePaymentMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<CreatePaymentMutation, CreatePaymentMutationVariables>(CreatePaymentDocument, options);
      }
export type CreatePaymentMutationHookResult = ReturnType<typeof useCreatePaymentMutation>;
export type CreatePaymentMutationResult = Apollo.MutationResult<CreatePaymentMutation>;
export type CreatePaymentMutationOptions = Apollo.BaseMutationOptions<CreatePaymentMutation, CreatePaymentMutationVariables>;
export const ChangePaymentStatusDocument = gql`
    mutation changePaymentStatus($id: Float!, $status: String!) {
  changePaymentStatus(id: $id, status: $status) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ChangePaymentStatusMutationFn = Apollo.MutationFunction<ChangePaymentStatusMutation, ChangePaymentStatusMutationVariables>;

/**
 * __useChangePaymentStatusMutation__
 *
 * To run a mutation, you first call `useChangePaymentStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useChangePaymentStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [changePaymentStatusMutation, { data, loading, error }] = useChangePaymentStatusMutation({
 *   variables: {
 *      id: // value for 'id'
 *      status: // value for 'status'
 *   },
 * });
 */
export function useChangePaymentStatusMutation(baseOptions?: Apollo.MutationHookOptions<ChangePaymentStatusMutation, ChangePaymentStatusMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ChangePaymentStatusMutation, ChangePaymentStatusMutationVariables>(ChangePaymentStatusDocument, options);
      }
export type ChangePaymentStatusMutationHookResult = ReturnType<typeof useChangePaymentStatusMutation>;
export type ChangePaymentStatusMutationResult = Apollo.MutationResult<ChangePaymentStatusMutation>;
export type ChangePaymentStatusMutationOptions = Apollo.BaseMutationOptions<ChangePaymentStatusMutation, ChangePaymentStatusMutationVariables>;
export const ForgotPasswordDocument = gql`
    mutation forgotPassword($email: String!) {
  forgotPassword(email: $email) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ForgotPasswordMutationFn = Apollo.MutationFunction<ForgotPasswordMutation, ForgotPasswordMutationVariables>;

/**
 * __useForgotPasswordMutation__
 *
 * To run a mutation, you first call `useForgotPasswordMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useForgotPasswordMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [forgotPasswordMutation, { data, loading, error }] = useForgotPasswordMutation({
 *   variables: {
 *      email: // value for 'email'
 *   },
 * });
 */
export function useForgotPasswordMutation(baseOptions?: Apollo.MutationHookOptions<ForgotPasswordMutation, ForgotPasswordMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ForgotPasswordMutation, ForgotPasswordMutationVariables>(ForgotPasswordDocument, options);
      }
export type ForgotPasswordMutationHookResult = ReturnType<typeof useForgotPasswordMutation>;
export type ForgotPasswordMutationResult = Apollo.MutationResult<ForgotPasswordMutation>;
export type ForgotPasswordMutationOptions = Apollo.BaseMutationOptions<ForgotPasswordMutation, ForgotPasswordMutationVariables>;
export const LoginDocument = gql`
    mutation Login($params: EmailPasswordArgs!) {
  login(params: $params) {
    user {
      id
      company {
        id
      }
    }
    token
    error {
      ...Error
    }
  }
}
    ${ErrorFragmentDoc}`;
export type LoginMutationFn = Apollo.MutationFunction<LoginMutation, LoginMutationVariables>;

/**
 * __useLoginMutation__
 *
 * To run a mutation, you first call `useLoginMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useLoginMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [loginMutation, { data, loading, error }] = useLoginMutation({
 *   variables: {
 *      params: // value for 'params'
 *   },
 * });
 */
export function useLoginMutation(baseOptions?: Apollo.MutationHookOptions<LoginMutation, LoginMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<LoginMutation, LoginMutationVariables>(LoginDocument, options);
      }
export type LoginMutationHookResult = ReturnType<typeof useLoginMutation>;
export type LoginMutationResult = Apollo.MutationResult<LoginMutation>;
export type LoginMutationOptions = Apollo.BaseMutationOptions<LoginMutation, LoginMutationVariables>;
export const LogoutDocument = gql`
    mutation Logout {
  logout
}
    `;
export type LogoutMutationFn = Apollo.MutationFunction<LogoutMutation, LogoutMutationVariables>;

/**
 * __useLogoutMutation__
 *
 * To run a mutation, you first call `useLogoutMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useLogoutMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [logoutMutation, { data, loading, error }] = useLogoutMutation({
 *   variables: {
 *   },
 * });
 */
export function useLogoutMutation(baseOptions?: Apollo.MutationHookOptions<LogoutMutation, LogoutMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<LogoutMutation, LogoutMutationVariables>(LogoutDocument, options);
      }
export type LogoutMutationHookResult = ReturnType<typeof useLogoutMutation>;
export type LogoutMutationResult = Apollo.MutationResult<LogoutMutation>;
export type LogoutMutationOptions = Apollo.BaseMutationOptions<LogoutMutation, LogoutMutationVariables>;
export const ResetPasswordDocument = gql`
    mutation resetPassword($token: String!, $newPassword: String!) {
  resetPassword(token: $token, newPassword: $newPassword) {
    user {
      ...Me
    }
    error {
      ...Error
    }
  }
}
    ${MeFragmentDoc}
${ErrorFragmentDoc}`;
export type ResetPasswordMutationFn = Apollo.MutationFunction<ResetPasswordMutation, ResetPasswordMutationVariables>;

/**
 * __useResetPasswordMutation__
 *
 * To run a mutation, you first call `useResetPasswordMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useResetPasswordMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [resetPasswordMutation, { data, loading, error }] = useResetPasswordMutation({
 *   variables: {
 *      token: // value for 'token'
 *      newPassword: // value for 'newPassword'
 *   },
 * });
 */
export function useResetPasswordMutation(baseOptions?: Apollo.MutationHookOptions<ResetPasswordMutation, ResetPasswordMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ResetPasswordMutation, ResetPasswordMutationVariables>(ResetPasswordDocument, options);
      }
export type ResetPasswordMutationHookResult = ReturnType<typeof useResetPasswordMutation>;
export type ResetPasswordMutationResult = Apollo.MutationResult<ResetPasswordMutation>;
export type ResetPasswordMutationOptions = Apollo.BaseMutationOptions<ResetPasswordMutation, ResetPasswordMutationVariables>;
export const RegisterDocument = gql`
    mutation register($params: RegisterUserArgs!) {
  register(params: $params) {
    status
    error {
      ...Error
    }
  }
}
    ${ErrorFragmentDoc}`;
export type RegisterMutationFn = Apollo.MutationFunction<RegisterMutation, RegisterMutationVariables>;

/**
 * __useRegisterMutation__
 *
 * To run a mutation, you first call `useRegisterMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useRegisterMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [registerMutation, { data, loading, error }] = useRegisterMutation({
 *   variables: {
 *      params: // value for 'params'
 *   },
 * });
 */
export function useRegisterMutation(baseOptions?: Apollo.MutationHookOptions<RegisterMutation, RegisterMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<RegisterMutation, RegisterMutationVariables>(RegisterDocument, options);
      }
export type RegisterMutationHookResult = ReturnType<typeof useRegisterMutation>;
export type RegisterMutationResult = Apollo.MutationResult<RegisterMutation>;
export type RegisterMutationOptions = Apollo.BaseMutationOptions<RegisterMutation, RegisterMutationVariables>;
export const EditUserDocument = gql`
    mutation editUser($id: Float!, $params: EditUserArgs!) {
  editUser(id: $id, params: $params) {
    status
    error {
      target
      message
    }
  }
}
    `;
export type EditUserMutationFn = Apollo.MutationFunction<EditUserMutation, EditUserMutationVariables>;

/**
 * __useEditUserMutation__
 *
 * To run a mutation, you first call `useEditUserMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditUserMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editUserMutation, { data, loading, error }] = useEditUserMutation({
 *   variables: {
 *      id: // value for 'id'
 *      params: // value for 'params'
 *   },
 * });
 */
export function useEditUserMutation(baseOptions?: Apollo.MutationHookOptions<EditUserMutation, EditUserMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<EditUserMutation, EditUserMutationVariables>(EditUserDocument, options);
      }
export type EditUserMutationHookResult = ReturnType<typeof useEditUserMutation>;
export type EditUserMutationResult = Apollo.MutationResult<EditUserMutation>;
export type EditUserMutationOptions = Apollo.BaseMutationOptions<EditUserMutation, EditUserMutationVariables>;
export const ChangeEmployeeStatusDocument = gql`
    mutation changeEmployeeStatus($employeeId: Float!, $status: String!) {
  changeEmployeeStatus(employeeId: $employeeId, status: $status) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ChangeEmployeeStatusMutationFn = Apollo.MutationFunction<ChangeEmployeeStatusMutation, ChangeEmployeeStatusMutationVariables>;

/**
 * __useChangeEmployeeStatusMutation__
 *
 * To run a mutation, you first call `useChangeEmployeeStatusMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useChangeEmployeeStatusMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [changeEmployeeStatusMutation, { data, loading, error }] = useChangeEmployeeStatusMutation({
 *   variables: {
 *      employeeId: // value for 'employeeId'
 *      status: // value for 'status'
 *   },
 * });
 */
export function useChangeEmployeeStatusMutation(baseOptions?: Apollo.MutationHookOptions<ChangeEmployeeStatusMutation, ChangeEmployeeStatusMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ChangeEmployeeStatusMutation, ChangeEmployeeStatusMutationVariables>(ChangeEmployeeStatusDocument, options);
      }
export type ChangeEmployeeStatusMutationHookResult = ReturnType<typeof useChangeEmployeeStatusMutation>;
export type ChangeEmployeeStatusMutationResult = Apollo.MutationResult<ChangeEmployeeStatusMutation>;
export type ChangeEmployeeStatusMutationOptions = Apollo.BaseMutationOptions<ChangeEmployeeStatusMutation, ChangeEmployeeStatusMutationVariables>;
export const ChangeEmployeeRoleDocument = gql`
    mutation changeEmployeeRole($employeeId: Float!, $companyRole: Float!, $departmentId: Float!, $designation: String!) {
  changeEmployeeRole(
    employeeId: $employeeId
    companyRole: $companyRole
    departmentId: $departmentId
    designation: $designation
  ) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ChangeEmployeeRoleMutationFn = Apollo.MutationFunction<ChangeEmployeeRoleMutation, ChangeEmployeeRoleMutationVariables>;

/**
 * __useChangeEmployeeRoleMutation__
 *
 * To run a mutation, you first call `useChangeEmployeeRoleMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useChangeEmployeeRoleMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [changeEmployeeRoleMutation, { data, loading, error }] = useChangeEmployeeRoleMutation({
 *   variables: {
 *      employeeId: // value for 'employeeId'
 *      companyRole: // value for 'companyRole'
 *      departmentId: // value for 'departmentId'
 *      designation: // value for 'designation'
 *   },
 * });
 */
export function useChangeEmployeeRoleMutation(baseOptions?: Apollo.MutationHookOptions<ChangeEmployeeRoleMutation, ChangeEmployeeRoleMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ChangeEmployeeRoleMutation, ChangeEmployeeRoleMutationVariables>(ChangeEmployeeRoleDocument, options);
      }
export type ChangeEmployeeRoleMutationHookResult = ReturnType<typeof useChangeEmployeeRoleMutation>;
export type ChangeEmployeeRoleMutationResult = Apollo.MutationResult<ChangeEmployeeRoleMutation>;
export type ChangeEmployeeRoleMutationOptions = Apollo.BaseMutationOptions<ChangeEmployeeRoleMutation, ChangeEmployeeRoleMutationVariables>;
export const ChangePasswordDocument = gql`
    mutation changePassword($currentPassword: String!, $newPassword: String!) {
  changePassword(currentPassword: $currentPassword, newPassword: $newPassword) {
    ...BooleanResponse
  }
}
    ${BooleanResponseFragmentDoc}`;
export type ChangePasswordMutationFn = Apollo.MutationFunction<ChangePasswordMutation, ChangePasswordMutationVariables>;

/**
 * __useChangePasswordMutation__
 *
 * To run a mutation, you first call `useChangePasswordMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useChangePasswordMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [changePasswordMutation, { data, loading, error }] = useChangePasswordMutation({
 *   variables: {
 *      currentPassword: // value for 'currentPassword'
 *      newPassword: // value for 'newPassword'
 *   },
 * });
 */
export function useChangePasswordMutation(baseOptions?: Apollo.MutationHookOptions<ChangePasswordMutation, ChangePasswordMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ChangePasswordMutation, ChangePasswordMutationVariables>(ChangePasswordDocument, options);
      }
export type ChangePasswordMutationHookResult = ReturnType<typeof useChangePasswordMutation>;
export type ChangePasswordMutationResult = Apollo.MutationResult<ChangePasswordMutation>;
export type ChangePasswordMutationOptions = Apollo.BaseMutationOptions<ChangePasswordMutation, ChangePasswordMutationVariables>;
export const GetCompanyDocument = gql`
    query getCompany($id: Float!) {
  getCompany(id: $id) {
    id
    name
    tinNumber
    registrationNumber
    type
    employees {
      id
      role {
        name
      }
    }
    features {
      id
      name
    }
    location
  }
}
    `;

/**
 * __useGetCompanyQuery__
 *
 * To run a query within a React component, call `useGetCompanyQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCompanyQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCompanyQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetCompanyQuery(baseOptions: Apollo.QueryHookOptions<GetCompanyQuery, GetCompanyQueryVariables> & ({ variables: GetCompanyQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetCompanyQuery, GetCompanyQueryVariables>(GetCompanyDocument, options);
      }
export function useGetCompanyLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetCompanyQuery, GetCompanyQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetCompanyQuery, GetCompanyQueryVariables>(GetCompanyDocument, options);
        }
export function useGetCompanySuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetCompanyQuery, GetCompanyQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetCompanyQuery, GetCompanyQueryVariables>(GetCompanyDocument, options);
        }
export type GetCompanyQueryHookResult = ReturnType<typeof useGetCompanyQuery>;
export type GetCompanyLazyQueryHookResult = ReturnType<typeof useGetCompanyLazyQuery>;
export type GetCompanySuspenseQueryHookResult = ReturnType<typeof useGetCompanySuspenseQuery>;
export type GetCompanyQueryResult = Apollo.QueryResult<GetCompanyQuery, GetCompanyQueryVariables>;
export const GetCompaniesDocument = gql`
    query getCompanies {
  getCompanies {
    id
    name
    tinNumber
    registrationNumber
    type
    employees {
      id
      role {
        name
      }
    }
    features {
      id
      name
    }
    payments {
      id
      status
      startDate
      endDate
      packageName
      billingCycle
      amount
    }
    location
  }
}
    `;

/**
 * __useGetCompaniesQuery__
 *
 * To run a query within a React component, call `useGetCompaniesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCompaniesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCompaniesQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetCompaniesQuery(baseOptions?: Apollo.QueryHookOptions<GetCompaniesQuery, GetCompaniesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetCompaniesQuery, GetCompaniesQueryVariables>(GetCompaniesDocument, options);
      }
export function useGetCompaniesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetCompaniesQuery, GetCompaniesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetCompaniesQuery, GetCompaniesQueryVariables>(GetCompaniesDocument, options);
        }
export function useGetCompaniesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetCompaniesQuery, GetCompaniesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetCompaniesQuery, GetCompaniesQueryVariables>(GetCompaniesDocument, options);
        }
export type GetCompaniesQueryHookResult = ReturnType<typeof useGetCompaniesQuery>;
export type GetCompaniesLazyQueryHookResult = ReturnType<typeof useGetCompaniesLazyQuery>;
export type GetCompaniesSuspenseQueryHookResult = ReturnType<typeof useGetCompaniesSuspenseQuery>;
export type GetCompaniesQueryResult = Apollo.QueryResult<GetCompaniesQuery, GetCompaniesQueryVariables>;
export const GetSchedulesDocument = gql`
    query getSchedules($ownerId: Int!, $owner: String!) {
  getSchedules(ownerId: $ownerId, owner: $owner) {
    id
    onTime
    offTime
    day
    description
    clinicId
    employeeId
  }
}
    `;

/**
 * __useGetSchedulesQuery__
 *
 * To run a query within a React component, call `useGetSchedulesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSchedulesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSchedulesQuery({
 *   variables: {
 *      ownerId: // value for 'ownerId'
 *      owner: // value for 'owner'
 *   },
 * });
 */
export function useGetSchedulesQuery(baseOptions: Apollo.QueryHookOptions<GetSchedulesQuery, GetSchedulesQueryVariables> & ({ variables: GetSchedulesQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSchedulesQuery, GetSchedulesQueryVariables>(GetSchedulesDocument, options);
      }
export function useGetSchedulesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSchedulesQuery, GetSchedulesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSchedulesQuery, GetSchedulesQueryVariables>(GetSchedulesDocument, options);
        }
export function useGetSchedulesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetSchedulesQuery, GetSchedulesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetSchedulesQuery, GetSchedulesQueryVariables>(GetSchedulesDocument, options);
        }
export type GetSchedulesQueryHookResult = ReturnType<typeof useGetSchedulesQuery>;
export type GetSchedulesLazyQueryHookResult = ReturnType<typeof useGetSchedulesLazyQuery>;
export type GetSchedulesSuspenseQueryHookResult = ReturnType<typeof useGetSchedulesSuspenseQuery>;
export type GetSchedulesQueryResult = Apollo.QueryResult<GetSchedulesQuery, GetSchedulesQueryVariables>;
export const GetEmployeesDocument = gql`
    query getEmployees {
  getEmployees {
    ...Me
  }
}
    ${MeFragmentDoc}`;

/**
 * __useGetEmployeesQuery__
 *
 * To run a query within a React component, call `useGetEmployeesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetEmployeesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetEmployeesQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetEmployeesQuery(baseOptions?: Apollo.QueryHookOptions<GetEmployeesQuery, GetEmployeesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetEmployeesQuery, GetEmployeesQueryVariables>(GetEmployeesDocument, options);
      }
export function useGetEmployeesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetEmployeesQuery, GetEmployeesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetEmployeesQuery, GetEmployeesQueryVariables>(GetEmployeesDocument, options);
        }
export function useGetEmployeesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetEmployeesQuery, GetEmployeesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetEmployeesQuery, GetEmployeesQueryVariables>(GetEmployeesDocument, options);
        }
export type GetEmployeesQueryHookResult = ReturnType<typeof useGetEmployeesQuery>;
export type GetEmployeesLazyQueryHookResult = ReturnType<typeof useGetEmployeesLazyQuery>;
export type GetEmployeesSuspenseQueryHookResult = ReturnType<typeof useGetEmployeesSuspenseQuery>;
export type GetEmployeesQueryResult = Apollo.QueryResult<GetEmployeesQuery, GetEmployeesQueryVariables>;
export const GetUndetailedEmployeesDocument = gql`
    query getUndetailedEmployees($companyId: Float!) {
  getUndetailedEmployees(companyId: $companyId) {
    firstname
    lastname
    email
    image
  }
}
    `;

/**
 * __useGetUndetailedEmployeesQuery__
 *
 * To run a query within a React component, call `useGetUndetailedEmployeesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUndetailedEmployeesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUndetailedEmployeesQuery({
 *   variables: {
 *      companyId: // value for 'companyId'
 *   },
 * });
 */
export function useGetUndetailedEmployeesQuery(baseOptions: Apollo.QueryHookOptions<GetUndetailedEmployeesQuery, GetUndetailedEmployeesQueryVariables> & ({ variables: GetUndetailedEmployeesQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetUndetailedEmployeesQuery, GetUndetailedEmployeesQueryVariables>(GetUndetailedEmployeesDocument, options);
      }
export function useGetUndetailedEmployeesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetUndetailedEmployeesQuery, GetUndetailedEmployeesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetUndetailedEmployeesQuery, GetUndetailedEmployeesQueryVariables>(GetUndetailedEmployeesDocument, options);
        }
export function useGetUndetailedEmployeesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetUndetailedEmployeesQuery, GetUndetailedEmployeesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetUndetailedEmployeesQuery, GetUndetailedEmployeesQueryVariables>(GetUndetailedEmployeesDocument, options);
        }
export type GetUndetailedEmployeesQueryHookResult = ReturnType<typeof useGetUndetailedEmployeesQuery>;
export type GetUndetailedEmployeesLazyQueryHookResult = ReturnType<typeof useGetUndetailedEmployeesLazyQuery>;
export type GetUndetailedEmployeesSuspenseQueryHookResult = ReturnType<typeof useGetUndetailedEmployeesSuspenseQuery>;
export type GetUndetailedEmployeesQueryResult = Apollo.QueryResult<GetUndetailedEmployeesQuery, GetUndetailedEmployeesQueryVariables>;
export const GetDepartmentsDocument = gql`
    query getDepartments {
  getDepartments {
    id
    name
    type
    description
    status
    parentId
    headOfDepartment {
      id
    }
    employees {
      id
    }
    clinics {
      id
      name
      clinicType
      size
      status
      description
    }
  }
}
    `;

/**
 * __useGetDepartmentsQuery__
 *
 * To run a query within a React component, call `useGetDepartmentsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetDepartmentsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetDepartmentsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetDepartmentsQuery(baseOptions?: Apollo.QueryHookOptions<GetDepartmentsQuery, GetDepartmentsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetDepartmentsQuery, GetDepartmentsQueryVariables>(GetDepartmentsDocument, options);
      }
export function useGetDepartmentsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetDepartmentsQuery, GetDepartmentsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetDepartmentsQuery, GetDepartmentsQueryVariables>(GetDepartmentsDocument, options);
        }
export function useGetDepartmentsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetDepartmentsQuery, GetDepartmentsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetDepartmentsQuery, GetDepartmentsQueryVariables>(GetDepartmentsDocument, options);
        }
export type GetDepartmentsQueryHookResult = ReturnType<typeof useGetDepartmentsQuery>;
export type GetDepartmentsLazyQueryHookResult = ReturnType<typeof useGetDepartmentsLazyQuery>;
export type GetDepartmentsSuspenseQueryHookResult = ReturnType<typeof useGetDepartmentsSuspenseQuery>;
export type GetDepartmentsQueryResult = Apollo.QueryResult<GetDepartmentsQuery, GetDepartmentsQueryVariables>;
export const GetClinicsDocument = gql`
    query getClinics {
  getClinics {
    id
    name
    description
    status
    clinicType
    size
    department {
      id
      name
    }
    leader {
      id
    }
  }
}
    `;

/**
 * __useGetClinicsQuery__
 *
 * To run a query within a React component, call `useGetClinicsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetClinicsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetClinicsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetClinicsQuery(baseOptions?: Apollo.QueryHookOptions<GetClinicsQuery, GetClinicsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetClinicsQuery, GetClinicsQueryVariables>(GetClinicsDocument, options);
      }
export function useGetClinicsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetClinicsQuery, GetClinicsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetClinicsQuery, GetClinicsQueryVariables>(GetClinicsDocument, options);
        }
export function useGetClinicsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetClinicsQuery, GetClinicsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetClinicsQuery, GetClinicsQueryVariables>(GetClinicsDocument, options);
        }
export type GetClinicsQueryHookResult = ReturnType<typeof useGetClinicsQuery>;
export type GetClinicsLazyQueryHookResult = ReturnType<typeof useGetClinicsLazyQuery>;
export type GetClinicsSuspenseQueryHookResult = ReturnType<typeof useGetClinicsSuspenseQuery>;
export type GetClinicsQueryResult = Apollo.QueryResult<GetClinicsQuery, GetClinicsQueryVariables>;
export const GetClinicDocument = gql`
    query getClinic($id: Int!) {
  getClinic(id: $id) {
    id
    name
    description
    status
    clinicType
    size
    department {
      id
      name
    }
    leader {
      id
    }
  }
}
    `;

/**
 * __useGetClinicQuery__
 *
 * To run a query within a React component, call `useGetClinicQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetClinicQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetClinicQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetClinicQuery(baseOptions: Apollo.QueryHookOptions<GetClinicQuery, GetClinicQueryVariables> & ({ variables: GetClinicQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetClinicQuery, GetClinicQueryVariables>(GetClinicDocument, options);
      }
export function useGetClinicLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetClinicQuery, GetClinicQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetClinicQuery, GetClinicQueryVariables>(GetClinicDocument, options);
        }
export function useGetClinicSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetClinicQuery, GetClinicQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetClinicQuery, GetClinicQueryVariables>(GetClinicDocument, options);
        }
export type GetClinicQueryHookResult = ReturnType<typeof useGetClinicQuery>;
export type GetClinicLazyQueryHookResult = ReturnType<typeof useGetClinicLazyQuery>;
export type GetClinicSuspenseQueryHookResult = ReturnType<typeof useGetClinicSuspenseQuery>;
export type GetClinicQueryResult = Apollo.QueryResult<GetClinicQuery, GetClinicQueryVariables>;
export const GetUsersDocument = gql`
    query getUsers($roles: [Float!]) {
  getUsers(roles: $roles) {
    id
    firstname
    middlename
    lastname
    email
    phone
    companyId
    company {
      id
      name
    }
    role {
      id
      name
    }
    employee {
      id
      status
      designation
      role {
        id
        name
      }
    }
  }
}
    `;

/**
 * __useGetUsersQuery__
 *
 * To run a query within a React component, call `useGetUsersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUsersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUsersQuery({
 *   variables: {
 *      roles: // value for 'roles'
 *   },
 * });
 */
export function useGetUsersQuery(baseOptions?: Apollo.QueryHookOptions<GetUsersQuery, GetUsersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetUsersQuery, GetUsersQueryVariables>(GetUsersDocument, options);
      }
export function useGetUsersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetUsersQuery, GetUsersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetUsersQuery, GetUsersQueryVariables>(GetUsersDocument, options);
        }
export function useGetUsersSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetUsersQuery, GetUsersQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetUsersQuery, GetUsersQueryVariables>(GetUsersDocument, options);
        }
export type GetUsersQueryHookResult = ReturnType<typeof useGetUsersQuery>;
export type GetUsersLazyQueryHookResult = ReturnType<typeof useGetUsersLazyQuery>;
export type GetUsersSuspenseQueryHookResult = ReturnType<typeof useGetUsersSuspenseQuery>;
export type GetUsersQueryResult = Apollo.QueryResult<GetUsersQuery, GetUsersQueryVariables>;
export const GetUserDocument = gql`
    query getUser($id: Float!) {
  getUser(id: $id) {
    id
    firstname
    middlename
    lastname
    email
    phone
    image
    companyId
    company {
      id
      name
    }
    role {
      id
      name
    }
    employee {
      id
      status
      designation
      role {
        id
        name
      }
    }
    permissions {
      id
      name
    }
  }
}
    `;

/**
 * __useGetUserQuery__
 *
 * To run a query within a React component, call `useGetUserQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetUserQuery(baseOptions: Apollo.QueryHookOptions<GetUserQuery, GetUserQueryVariables> & ({ variables: GetUserQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetUserQuery, GetUserQueryVariables>(GetUserDocument, options);
      }
export function useGetUserLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetUserQuery, GetUserQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetUserQuery, GetUserQueryVariables>(GetUserDocument, options);
        }
export function useGetUserSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetUserQuery, GetUserQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetUserQuery, GetUserQueryVariables>(GetUserDocument, options);
        }
export type GetUserQueryHookResult = ReturnType<typeof useGetUserQuery>;
export type GetUserLazyQueryHookResult = ReturnType<typeof useGetUserLazyQuery>;
export type GetUserSuspenseQueryHookResult = ReturnType<typeof useGetUserSuspenseQuery>;
export type GetUserQueryResult = Apollo.QueryResult<GetUserQuery, GetUserQueryVariables>;
export const GetAllItemsDocument = gql`
    query getAllItems {
  getAllItems {
    id
    name
    type
    barcode
    description
    image
    sellingPrice
    reorder
    reference
    internal
    unit
    stock
    units {
      id
      name
      quantity
      price
    }
  }
}
    `;

/**
 * __useGetAllItemsQuery__
 *
 * To run a query within a React component, call `useGetAllItemsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAllItemsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAllItemsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetAllItemsQuery(baseOptions?: Apollo.QueryHookOptions<GetAllItemsQuery, GetAllItemsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetAllItemsQuery, GetAllItemsQueryVariables>(GetAllItemsDocument, options);
      }
export function useGetAllItemsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetAllItemsQuery, GetAllItemsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetAllItemsQuery, GetAllItemsQueryVariables>(GetAllItemsDocument, options);
        }
export function useGetAllItemsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetAllItemsQuery, GetAllItemsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetAllItemsQuery, GetAllItemsQueryVariables>(GetAllItemsDocument, options);
        }
export type GetAllItemsQueryHookResult = ReturnType<typeof useGetAllItemsQuery>;
export type GetAllItemsLazyQueryHookResult = ReturnType<typeof useGetAllItemsLazyQuery>;
export type GetAllItemsSuspenseQueryHookResult = ReturnType<typeof useGetAllItemsSuspenseQuery>;
export type GetAllItemsQueryResult = Apollo.QueryResult<GetAllItemsQuery, GetAllItemsQueryVariables>;
export const GetAllServicesDocument = gql`
    query getAllServices {
  getAllServices {
    id
    name
    description
    sellingPrice
    reference
  }
}
    `;

/**
 * __useGetAllServicesQuery__
 *
 * To run a query within a React component, call `useGetAllServicesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAllServicesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAllServicesQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetAllServicesQuery(baseOptions?: Apollo.QueryHookOptions<GetAllServicesQuery, GetAllServicesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetAllServicesQuery, GetAllServicesQueryVariables>(GetAllServicesDocument, options);
      }
export function useGetAllServicesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetAllServicesQuery, GetAllServicesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetAllServicesQuery, GetAllServicesQueryVariables>(GetAllServicesDocument, options);
        }
export function useGetAllServicesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetAllServicesQuery, GetAllServicesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetAllServicesQuery, GetAllServicesQueryVariables>(GetAllServicesDocument, options);
        }
export type GetAllServicesQueryHookResult = ReturnType<typeof useGetAllServicesQuery>;
export type GetAllServicesLazyQueryHookResult = ReturnType<typeof useGetAllServicesLazyQuery>;
export type GetAllServicesSuspenseQueryHookResult = ReturnType<typeof useGetAllServicesSuspenseQuery>;
export type GetAllServicesQueryResult = Apollo.QueryResult<GetAllServicesQuery, GetAllServicesQueryVariables>;
export const GetStoreItemsDocument = gql`
    query getStoreItems($storeId: Float!) {
  getStoreItems(storeId: $storeId) {
    id
    name
    type
    image
    barcode
    description
    sellingPrice
    reorder
    reference
    internal
    unit
    stock
    units {
      id
      name
      quantity
      price
    }
  }
}
    `;

/**
 * __useGetStoreItemsQuery__
 *
 * To run a query within a React component, call `useGetStoreItemsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetStoreItemsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetStoreItemsQuery({
 *   variables: {
 *      storeId: // value for 'storeId'
 *   },
 * });
 */
export function useGetStoreItemsQuery(baseOptions: Apollo.QueryHookOptions<GetStoreItemsQuery, GetStoreItemsQueryVariables> & ({ variables: GetStoreItemsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetStoreItemsQuery, GetStoreItemsQueryVariables>(GetStoreItemsDocument, options);
      }
export function useGetStoreItemsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetStoreItemsQuery, GetStoreItemsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetStoreItemsQuery, GetStoreItemsQueryVariables>(GetStoreItemsDocument, options);
        }
export function useGetStoreItemsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetStoreItemsQuery, GetStoreItemsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetStoreItemsQuery, GetStoreItemsQueryVariables>(GetStoreItemsDocument, options);
        }
export type GetStoreItemsQueryHookResult = ReturnType<typeof useGetStoreItemsQuery>;
export type GetStoreItemsLazyQueryHookResult = ReturnType<typeof useGetStoreItemsLazyQuery>;
export type GetStoreItemsSuspenseQueryHookResult = ReturnType<typeof useGetStoreItemsSuspenseQuery>;
export type GetStoreItemsQueryResult = Apollo.QueryResult<GetStoreItemsQuery, GetStoreItemsQueryVariables>;
export const GetInternalItemsDocument = gql`
    query getInternalItems {
  getInternalItems {
    id
    name
    type
    image
    barcode
    description
    reorder
    reference
    internal
    unit
    stock
    units {
      id
      name
      quantity
      price
    }
  }
}
    `;

/**
 * __useGetInternalItemsQuery__
 *
 * To run a query within a React component, call `useGetInternalItemsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetInternalItemsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetInternalItemsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetInternalItemsQuery(baseOptions?: Apollo.QueryHookOptions<GetInternalItemsQuery, GetInternalItemsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetInternalItemsQuery, GetInternalItemsQueryVariables>(GetInternalItemsDocument, options);
      }
export function useGetInternalItemsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetInternalItemsQuery, GetInternalItemsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetInternalItemsQuery, GetInternalItemsQueryVariables>(GetInternalItemsDocument, options);
        }
export function useGetInternalItemsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetInternalItemsQuery, GetInternalItemsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetInternalItemsQuery, GetInternalItemsQueryVariables>(GetInternalItemsDocument, options);
        }
export type GetInternalItemsQueryHookResult = ReturnType<typeof useGetInternalItemsQuery>;
export type GetInternalItemsLazyQueryHookResult = ReturnType<typeof useGetInternalItemsLazyQuery>;
export type GetInternalItemsSuspenseQueryHookResult = ReturnType<typeof useGetInternalItemsSuspenseQuery>;
export type GetInternalItemsQueryResult = Apollo.QueryResult<GetInternalItemsQuery, GetInternalItemsQueryVariables>;
export const GetMerchandiseItemsDocument = gql`
    query getMerchandiseItems {
  getMerchandiseItems {
    id
    name
    type
    barcode
    image
    description
    sellingPrice
    reorder
    reference
    internal
    unit
    stock
    units {
      id
      name
      quantity
      price
    }
  }
}
    `;

/**
 * __useGetMerchandiseItemsQuery__
 *
 * To run a query within a React component, call `useGetMerchandiseItemsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMerchandiseItemsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMerchandiseItemsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetMerchandiseItemsQuery(baseOptions?: Apollo.QueryHookOptions<GetMerchandiseItemsQuery, GetMerchandiseItemsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetMerchandiseItemsQuery, GetMerchandiseItemsQueryVariables>(GetMerchandiseItemsDocument, options);
      }
export function useGetMerchandiseItemsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetMerchandiseItemsQuery, GetMerchandiseItemsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetMerchandiseItemsQuery, GetMerchandiseItemsQueryVariables>(GetMerchandiseItemsDocument, options);
        }
export function useGetMerchandiseItemsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetMerchandiseItemsQuery, GetMerchandiseItemsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetMerchandiseItemsQuery, GetMerchandiseItemsQueryVariables>(GetMerchandiseItemsDocument, options);
        }
export type GetMerchandiseItemsQueryHookResult = ReturnType<typeof useGetMerchandiseItemsQuery>;
export type GetMerchandiseItemsLazyQueryHookResult = ReturnType<typeof useGetMerchandiseItemsLazyQuery>;
export type GetMerchandiseItemsSuspenseQueryHookResult = ReturnType<typeof useGetMerchandiseItemsSuspenseQuery>;
export type GetMerchandiseItemsQueryResult = Apollo.QueryResult<GetMerchandiseItemsQuery, GetMerchandiseItemsQueryVariables>;
export const GetItemDocument = gql`
    query getItem($id: Float!) {
  getItem(id: $id) {
    id
    name
    type
    barcode
    image
    description
    internal
    reorder
    reference
    internal
    unit
    stock
    sellingPrice
    units {
      id
      name
      quantity
      price
    }
    imports {
      id
      importDate
      supplier
      quantity
      importPrice
      sellingPrice
    }
    inventoryTransfers {
      id
      type
      details
      transferDate
      sourceStoreId
      destinationStoreId
    }
    transfers {
      id
      inventoryId
      itemId
      quantity
    }
  }
}
    `;

/**
 * __useGetItemQuery__
 *
 * To run a query within a React component, call `useGetItemQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetItemQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetItemQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetItemQuery(baseOptions: Apollo.QueryHookOptions<GetItemQuery, GetItemQueryVariables> & ({ variables: GetItemQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetItemQuery, GetItemQueryVariables>(GetItemDocument, options);
      }
export function useGetItemLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetItemQuery, GetItemQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetItemQuery, GetItemQueryVariables>(GetItemDocument, options);
        }
export function useGetItemSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetItemQuery, GetItemQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetItemQuery, GetItemQueryVariables>(GetItemDocument, options);
        }
export type GetItemQueryHookResult = ReturnType<typeof useGetItemQuery>;
export type GetItemLazyQueryHookResult = ReturnType<typeof useGetItemLazyQuery>;
export type GetItemSuspenseQueryHookResult = ReturnType<typeof useGetItemSuspenseQuery>;
export type GetItemQueryResult = Apollo.QueryResult<GetItemQuery, GetItemQueryVariables>;
export const GetSalesDocument = gql`
    query getSales($date: DateTime) {
  getSales(date: $date) {
    id
    createdAt
    details
    type
    granted
    dispatched
    sourceStoreId
    keeper {
      id
      userId
    }
    approver {
      id
      userId
    }
    bill {
      amount
      cleared
      paymentType
    }
    transfers {
      id
      itemId
      quantity
      details
    }
    items {
      id
      name
      unit
      sellingPrice
      imports {
        id
        importDate
        importPrice
      }
      type
      reference
      description
    }
  }
}
    `;

/**
 * __useGetSalesQuery__
 *
 * To run a query within a React component, call `useGetSalesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSalesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSalesQuery({
 *   variables: {
 *      date: // value for 'date'
 *   },
 * });
 */
export function useGetSalesQuery(baseOptions?: Apollo.QueryHookOptions<GetSalesQuery, GetSalesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSalesQuery, GetSalesQueryVariables>(GetSalesDocument, options);
      }
export function useGetSalesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSalesQuery, GetSalesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSalesQuery, GetSalesQueryVariables>(GetSalesDocument, options);
        }
export function useGetSalesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetSalesQuery, GetSalesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetSalesQuery, GetSalesQueryVariables>(GetSalesDocument, options);
        }
export type GetSalesQueryHookResult = ReturnType<typeof useGetSalesQuery>;
export type GetSalesLazyQueryHookResult = ReturnType<typeof useGetSalesLazyQuery>;
export type GetSalesSuspenseQueryHookResult = ReturnType<typeof useGetSalesSuspenseQuery>;
export type GetSalesQueryResult = Apollo.QueryResult<GetSalesQuery, GetSalesQueryVariables>;
export const GetSalesPosDocument = gql`
    query getSalesPOS {
  getSalesPOS {
    id
    createdAt
    details
    type
    granted
    dispatched
    sourceStoreId
    customerTag
    sourceStore {
      id
      name
    }
    consumer {
      id
      userId
      user {
        id
        firstname
        lastname
      }
    }
    keeper {
      id
      userId
      user {
        id
        firstname
        lastname
      }
    }
    bill {
      amount
      cleared
      paymentType
    }
    transfers {
      id
      itemId
      quantity
      details
      price
      dispatched
    }
    items {
      id
      name
      unit
      sellingPrice
      type
      reference
      description
    }
  }
}
    `;

/**
 * __useGetSalesPosQuery__
 *
 * To run a query within a React component, call `useGetSalesPosQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSalesPosQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSalesPosQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetSalesPosQuery(baseOptions?: Apollo.QueryHookOptions<GetSalesPosQuery, GetSalesPosQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetSalesPosQuery, GetSalesPosQueryVariables>(GetSalesPosDocument, options);
      }
export function useGetSalesPosLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetSalesPosQuery, GetSalesPosQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetSalesPosQuery, GetSalesPosQueryVariables>(GetSalesPosDocument, options);
        }
export function useGetSalesPosSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetSalesPosQuery, GetSalesPosQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetSalesPosQuery, GetSalesPosQueryVariables>(GetSalesPosDocument, options);
        }
export type GetSalesPosQueryHookResult = ReturnType<typeof useGetSalesPosQuery>;
export type GetSalesPosLazyQueryHookResult = ReturnType<typeof useGetSalesPosLazyQuery>;
export type GetSalesPosSuspenseQueryHookResult = ReturnType<typeof useGetSalesPosSuspenseQuery>;
export type GetSalesPosQueryResult = Apollo.QueryResult<GetSalesPosQuery, GetSalesPosQueryVariables>;
export const GetOpenTabsDocument = gql`
    query getOpenTabs {
  getOpenTabs {
    id
    createdAt
    details
    type
    granted
    dispatched
    sourceStoreId
    customerTag
    sourceStore {
      id
      name
    }
    consumer {
      id
      userId
      user {
        id
        firstname
        lastname
      }
    }
    keeper {
      id
      userId
      user {
        id
        firstname
        lastname
      }
    }
    bill {
      createdAt
      amount
      cleared
      paymentType
    }
    transfers {
      id
      itemId
      inventoryId
      quantity
      details
      price
      dispatched
    }
    items {
      id
      name
      unit
      sellingPrice
      type
      reference
      description
      stock
    }
  }
}
    `;

/**
 * __useGetOpenTabsQuery__
 *
 * To run a query within a React component, call `useGetOpenTabsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetOpenTabsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetOpenTabsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetOpenTabsQuery(baseOptions?: Apollo.QueryHookOptions<GetOpenTabsQuery, GetOpenTabsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetOpenTabsQuery, GetOpenTabsQueryVariables>(GetOpenTabsDocument, options);
      }
export function useGetOpenTabsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetOpenTabsQuery, GetOpenTabsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetOpenTabsQuery, GetOpenTabsQueryVariables>(GetOpenTabsDocument, options);
        }
export function useGetOpenTabsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetOpenTabsQuery, GetOpenTabsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetOpenTabsQuery, GetOpenTabsQueryVariables>(GetOpenTabsDocument, options);
        }
export type GetOpenTabsQueryHookResult = ReturnType<typeof useGetOpenTabsQuery>;
export type GetOpenTabsLazyQueryHookResult = ReturnType<typeof useGetOpenTabsLazyQuery>;
export type GetOpenTabsSuspenseQueryHookResult = ReturnType<typeof useGetOpenTabsSuspenseQuery>;
export type GetOpenTabsQueryResult = Apollo.QueryResult<GetOpenTabsQuery, GetOpenTabsQueryVariables>;
export const GetStoresDocument = gql`
    query getStores {
  getStores {
    id
    name
    primary
    address
    storeKeepers {
      id
    }
  }
}
    `;

/**
 * __useGetStoresQuery__
 *
 * To run a query within a React component, call `useGetStoresQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetStoresQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetStoresQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetStoresQuery(baseOptions?: Apollo.QueryHookOptions<GetStoresQuery, GetStoresQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetStoresQuery, GetStoresQueryVariables>(GetStoresDocument, options);
      }
export function useGetStoresLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetStoresQuery, GetStoresQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetStoresQuery, GetStoresQueryVariables>(GetStoresDocument, options);
        }
export function useGetStoresSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetStoresQuery, GetStoresQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetStoresQuery, GetStoresQueryVariables>(GetStoresDocument, options);
        }
export type GetStoresQueryHookResult = ReturnType<typeof useGetStoresQuery>;
export type GetStoresLazyQueryHookResult = ReturnType<typeof useGetStoresLazyQuery>;
export type GetStoresSuspenseQueryHookResult = ReturnType<typeof useGetStoresSuspenseQuery>;
export type GetStoresQueryResult = Apollo.QueryResult<GetStoresQuery, GetStoresQueryVariables>;
export const GetInventoryTransfersDocument = gql`
    query getInventoryTransfers($type: String) {
  getInventoryTransfers(type: $type) {
    id
    details
    type
    granted
    received
    transferDate
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
  }
}
    `;

/**
 * __useGetInventoryTransfersQuery__
 *
 * To run a query within a React component, call `useGetInventoryTransfersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetInventoryTransfersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetInventoryTransfersQuery({
 *   variables: {
 *      type: // value for 'type'
 *   },
 * });
 */
export function useGetInventoryTransfersQuery(baseOptions?: Apollo.QueryHookOptions<GetInventoryTransfersQuery, GetInventoryTransfersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetInventoryTransfersQuery, GetInventoryTransfersQueryVariables>(GetInventoryTransfersDocument, options);
      }
export function useGetInventoryTransfersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetInventoryTransfersQuery, GetInventoryTransfersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetInventoryTransfersQuery, GetInventoryTransfersQueryVariables>(GetInventoryTransfersDocument, options);
        }
export function useGetInventoryTransfersSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetInventoryTransfersQuery, GetInventoryTransfersQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetInventoryTransfersQuery, GetInventoryTransfersQueryVariables>(GetInventoryTransfersDocument, options);
        }
export type GetInventoryTransfersQueryHookResult = ReturnType<typeof useGetInventoryTransfersQuery>;
export type GetInventoryTransfersLazyQueryHookResult = ReturnType<typeof useGetInventoryTransfersLazyQuery>;
export type GetInventoryTransfersSuspenseQueryHookResult = ReturnType<typeof useGetInventoryTransfersSuspenseQuery>;
export type GetInventoryTransfersQueryResult = Apollo.QueryResult<GetInventoryTransfersQuery, GetInventoryTransfersQueryVariables>;
export const GetInventoryTransferDocument = gql`
    query getInventoryTransfer($id: Float!) {
  getInventoryTransfer(id: $id) {
    id
    details
    type
    granted
    received
    transferDate
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
    keeper {
      id
      userId
    }
    consumer {
      id
      userId
    }
    transfers {
      id
      itemId
      quantity
    }
    items {
      id
      name
      unit
      sellingPrice
      type
    }
  }
}
    `;

/**
 * __useGetInventoryTransferQuery__
 *
 * To run a query within a React component, call `useGetInventoryTransferQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetInventoryTransferQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetInventoryTransferQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetInventoryTransferQuery(baseOptions: Apollo.QueryHookOptions<GetInventoryTransferQuery, GetInventoryTransferQueryVariables> & ({ variables: GetInventoryTransferQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetInventoryTransferQuery, GetInventoryTransferQueryVariables>(GetInventoryTransferDocument, options);
      }
export function useGetInventoryTransferLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetInventoryTransferQuery, GetInventoryTransferQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetInventoryTransferQuery, GetInventoryTransferQueryVariables>(GetInventoryTransferDocument, options);
        }
export function useGetInventoryTransferSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetInventoryTransferQuery, GetInventoryTransferQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetInventoryTransferQuery, GetInventoryTransferQueryVariables>(GetInventoryTransferDocument, options);
        }
export type GetInventoryTransferQueryHookResult = ReturnType<typeof useGetInventoryTransferQuery>;
export type GetInventoryTransferLazyQueryHookResult = ReturnType<typeof useGetInventoryTransferLazyQuery>;
export type GetInventoryTransferSuspenseQueryHookResult = ReturnType<typeof useGetInventoryTransferSuspenseQuery>;
export type GetInventoryTransferQueryResult = Apollo.QueryResult<GetInventoryTransferQuery, GetInventoryTransferQueryVariables>;
export const GetItemTransfersDocument = gql`
    query getItemTransfers($itemId: Float!, $type: String) {
  getItemTransfers(itemId: $itemId, type: $type) {
    id
    details
    quantity
    inventoryTransfer {
      id
      updatedAt
      details
      type
      transferDate
      sourceStoreId
      destinationStoreId
    }
    item {
      id
      name
      unit
    }
  }
}
    `;

/**
 * __useGetItemTransfersQuery__
 *
 * To run a query within a React component, call `useGetItemTransfersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetItemTransfersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetItemTransfersQuery({
 *   variables: {
 *      itemId: // value for 'itemId'
 *      type: // value for 'type'
 *   },
 * });
 */
export function useGetItemTransfersQuery(baseOptions: Apollo.QueryHookOptions<GetItemTransfersQuery, GetItemTransfersQueryVariables> & ({ variables: GetItemTransfersQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetItemTransfersQuery, GetItemTransfersQueryVariables>(GetItemTransfersDocument, options);
      }
export function useGetItemTransfersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetItemTransfersQuery, GetItemTransfersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetItemTransfersQuery, GetItemTransfersQueryVariables>(GetItemTransfersDocument, options);
        }
export function useGetItemTransfersSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetItemTransfersQuery, GetItemTransfersQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetItemTransfersQuery, GetItemTransfersQueryVariables>(GetItemTransfersDocument, options);
        }
export type GetItemTransfersQueryHookResult = ReturnType<typeof useGetItemTransfersQuery>;
export type GetItemTransfersLazyQueryHookResult = ReturnType<typeof useGetItemTransfersLazyQuery>;
export type GetItemTransfersSuspenseQueryHookResult = ReturnType<typeof useGetItemTransfersSuspenseQuery>;
export type GetItemTransfersQueryResult = Apollo.QueryResult<GetItemTransfersQuery, GetItemTransfersQueryVariables>;
export const GetWriteOffsByCompanyDocument = gql`
    query GetWriteOffsByCompany {
  getWriteOffsByCompany {
    id
    createdAt
    quantity
    details
    item {
      id
      name
      unit
    }
  }
}
    `;

/**
 * __useGetWriteOffsByCompanyQuery__
 *
 * To run a query within a React component, call `useGetWriteOffsByCompanyQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetWriteOffsByCompanyQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetWriteOffsByCompanyQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetWriteOffsByCompanyQuery(baseOptions?: Apollo.QueryHookOptions<GetWriteOffsByCompanyQuery, GetWriteOffsByCompanyQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetWriteOffsByCompanyQuery, GetWriteOffsByCompanyQueryVariables>(GetWriteOffsByCompanyDocument, options);
      }
export function useGetWriteOffsByCompanyLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetWriteOffsByCompanyQuery, GetWriteOffsByCompanyQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetWriteOffsByCompanyQuery, GetWriteOffsByCompanyQueryVariables>(GetWriteOffsByCompanyDocument, options);
        }
export function useGetWriteOffsByCompanySuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetWriteOffsByCompanyQuery, GetWriteOffsByCompanyQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetWriteOffsByCompanyQuery, GetWriteOffsByCompanyQueryVariables>(GetWriteOffsByCompanyDocument, options);
        }
export type GetWriteOffsByCompanyQueryHookResult = ReturnType<typeof useGetWriteOffsByCompanyQuery>;
export type GetWriteOffsByCompanyLazyQueryHookResult = ReturnType<typeof useGetWriteOffsByCompanyLazyQuery>;
export type GetWriteOffsByCompanySuspenseQueryHookResult = ReturnType<typeof useGetWriteOffsByCompanySuspenseQuery>;
export type GetWriteOffsByCompanyQueryResult = Apollo.QueryResult<GetWriteOffsByCompanyQuery, GetWriteOffsByCompanyQueryVariables>;
export const GetDispatchesDocument = gql`
    query getDispatches {
  getDispatches {
    id
    updatedAt
    transferDate
    granted
    dispatched
    received
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
    items {
      id
      name
      unit
    }
    keeperId
    consumerId
    transfers {
      id
      itemId
      quantity
    }
  }
}
    `;

/**
 * __useGetDispatchesQuery__
 *
 * To run a query within a React component, call `useGetDispatchesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetDispatchesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetDispatchesQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetDispatchesQuery(baseOptions?: Apollo.QueryHookOptions<GetDispatchesQuery, GetDispatchesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetDispatchesQuery, GetDispatchesQueryVariables>(GetDispatchesDocument, options);
      }
export function useGetDispatchesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetDispatchesQuery, GetDispatchesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetDispatchesQuery, GetDispatchesQueryVariables>(GetDispatchesDocument, options);
        }
export function useGetDispatchesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetDispatchesQuery, GetDispatchesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetDispatchesQuery, GetDispatchesQueryVariables>(GetDispatchesDocument, options);
        }
export type GetDispatchesQueryHookResult = ReturnType<typeof useGetDispatchesQuery>;
export type GetDispatchesLazyQueryHookResult = ReturnType<typeof useGetDispatchesLazyQuery>;
export type GetDispatchesSuspenseQueryHookResult = ReturnType<typeof useGetDispatchesSuspenseQuery>;
export type GetDispatchesQueryResult = Apollo.QueryResult<GetDispatchesQuery, GetDispatchesQueryVariables>;
export const GetTransfersDocument = gql`
    query getTransfers {
  getTransfers {
    id
    updatedAt
    transferDate
    granted
    dispatched
    received
    sourceStore {
      id
      name
    }
    destinationStore {
      id
      name
    }
    items {
      id
      name
      unit
    }
    keeperId
    consumerId
    transfers {
      id
      itemId
      quantity
    }
  }
}
    `;

/**
 * __useGetTransfersQuery__
 *
 * To run a query within a React component, call `useGetTransfersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTransfersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTransfersQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetTransfersQuery(baseOptions?: Apollo.QueryHookOptions<GetTransfersQuery, GetTransfersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetTransfersQuery, GetTransfersQueryVariables>(GetTransfersDocument, options);
      }
export function useGetTransfersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetTransfersQuery, GetTransfersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetTransfersQuery, GetTransfersQueryVariables>(GetTransfersDocument, options);
        }
export function useGetTransfersSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetTransfersQuery, GetTransfersQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetTransfersQuery, GetTransfersQueryVariables>(GetTransfersDocument, options);
        }
export type GetTransfersQueryHookResult = ReturnType<typeof useGetTransfersQuery>;
export type GetTransfersLazyQueryHookResult = ReturnType<typeof useGetTransfersLazyQuery>;
export type GetTransfersSuspenseQueryHookResult = ReturnType<typeof useGetTransfersSuspenseQuery>;
export type GetTransfersQueryResult = Apollo.QueryResult<GetTransfersQuery, GetTransfersQueryVariables>;
export const GetItemBatchStocksDocument = gql`
    query getItemBatchStocks($itemId: Float!) {
  getItemBatchStocks(itemId: $itemId) {
    id
    batch
    expireDate
    stock
    storeItemStocks {
      id
      storeId
      stock
    }
  }
}
    `;

/**
 * __useGetItemBatchStocksQuery__
 *
 * To run a query within a React component, call `useGetItemBatchStocksQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetItemBatchStocksQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetItemBatchStocksQuery({
 *   variables: {
 *      itemId: // value for 'itemId'
 *   },
 * });
 */
export function useGetItemBatchStocksQuery(baseOptions: Apollo.QueryHookOptions<GetItemBatchStocksQuery, GetItemBatchStocksQueryVariables> & ({ variables: GetItemBatchStocksQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetItemBatchStocksQuery, GetItemBatchStocksQueryVariables>(GetItemBatchStocksDocument, options);
      }
export function useGetItemBatchStocksLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetItemBatchStocksQuery, GetItemBatchStocksQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetItemBatchStocksQuery, GetItemBatchStocksQueryVariables>(GetItemBatchStocksDocument, options);
        }
export function useGetItemBatchStocksSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetItemBatchStocksQuery, GetItemBatchStocksQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetItemBatchStocksQuery, GetItemBatchStocksQueryVariables>(GetItemBatchStocksDocument, options);
        }
export type GetItemBatchStocksQueryHookResult = ReturnType<typeof useGetItemBatchStocksQuery>;
export type GetItemBatchStocksLazyQueryHookResult = ReturnType<typeof useGetItemBatchStocksLazyQuery>;
export type GetItemBatchStocksSuspenseQueryHookResult = ReturnType<typeof useGetItemBatchStocksSuspenseQuery>;
export type GetItemBatchStocksQueryResult = Apollo.QueryResult<GetItemBatchStocksQuery, GetItemBatchStocksQueryVariables>;
export const GetBatchStockForStoreDocument = gql`
    query getBatchStockForStore($itemId: Float, $storeId: Float) {
  getBatchStockForStore(itemId: $itemId, storeId: $storeId) {
    itemId
    batch
    expireDate
    stock
    storeItemStocks {
      storeId
      stock
    }
  }
}
    `;

/**
 * __useGetBatchStockForStoreQuery__
 *
 * To run a query within a React component, call `useGetBatchStockForStoreQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetBatchStockForStoreQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetBatchStockForStoreQuery({
 *   variables: {
 *      itemId: // value for 'itemId'
 *      storeId: // value for 'storeId'
 *   },
 * });
 */
export function useGetBatchStockForStoreQuery(baseOptions?: Apollo.QueryHookOptions<GetBatchStockForStoreQuery, GetBatchStockForStoreQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetBatchStockForStoreQuery, GetBatchStockForStoreQueryVariables>(GetBatchStockForStoreDocument, options);
      }
export function useGetBatchStockForStoreLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetBatchStockForStoreQuery, GetBatchStockForStoreQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetBatchStockForStoreQuery, GetBatchStockForStoreQueryVariables>(GetBatchStockForStoreDocument, options);
        }
export function useGetBatchStockForStoreSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetBatchStockForStoreQuery, GetBatchStockForStoreQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetBatchStockForStoreQuery, GetBatchStockForStoreQueryVariables>(GetBatchStockForStoreDocument, options);
        }
export type GetBatchStockForStoreQueryHookResult = ReturnType<typeof useGetBatchStockForStoreQuery>;
export type GetBatchStockForStoreLazyQueryHookResult = ReturnType<typeof useGetBatchStockForStoreLazyQuery>;
export type GetBatchStockForStoreSuspenseQueryHookResult = ReturnType<typeof useGetBatchStockForStoreSuspenseQuery>;
export type GetBatchStockForStoreQueryResult = Apollo.QueryResult<GetBatchStockForStoreQuery, GetBatchStockForStoreQueryVariables>;
export const GetItemStoreStocksPosDocument = gql`
    query getItemStoreStocksPOS($itemId: Float!) {
  getItemStoreStocks(itemId: $itemId) {
    id
    storeId
    stock
    store {
      id
      name
    }
  }
}
    `;

/**
 * __useGetItemStoreStocksPosQuery__
 *
 * To run a query within a React component, call `useGetItemStoreStocksPosQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetItemStoreStocksPosQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetItemStoreStocksPosQuery({
 *   variables: {
 *      itemId: // value for 'itemId'
 *   },
 * });
 */
export function useGetItemStoreStocksPosQuery(baseOptions: Apollo.QueryHookOptions<GetItemStoreStocksPosQuery, GetItemStoreStocksPosQueryVariables> & ({ variables: GetItemStoreStocksPosQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetItemStoreStocksPosQuery, GetItemStoreStocksPosQueryVariables>(GetItemStoreStocksPosDocument, options);
      }
export function useGetItemStoreStocksPosLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetItemStoreStocksPosQuery, GetItemStoreStocksPosQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetItemStoreStocksPosQuery, GetItemStoreStocksPosQueryVariables>(GetItemStoreStocksPosDocument, options);
        }
export function useGetItemStoreStocksPosSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetItemStoreStocksPosQuery, GetItemStoreStocksPosQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetItemStoreStocksPosQuery, GetItemStoreStocksPosQueryVariables>(GetItemStoreStocksPosDocument, options);
        }
export type GetItemStoreStocksPosQueryHookResult = ReturnType<typeof useGetItemStoreStocksPosQuery>;
export type GetItemStoreStocksPosLazyQueryHookResult = ReturnType<typeof useGetItemStoreStocksPosLazyQuery>;
export type GetItemStoreStocksPosSuspenseQueryHookResult = ReturnType<typeof useGetItemStoreStocksPosSuspenseQuery>;
export type GetItemStoreStocksPosQueryResult = Apollo.QueryResult<GetItemStoreStocksPosQuery, GetItemStoreStocksPosQueryVariables>;
export const GetItemStoreStocksDocument = gql`
    query getItemStoreStocks($itemId: Float!) {
  getItemStoreStocks(itemId: $itemId) {
    id
    storeId
    stock
    store {
      id
      name
    }
  }
}
    `;

/**
 * __useGetItemStoreStocksQuery__
 *
 * To run a query within a React component, call `useGetItemStoreStocksQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetItemStoreStocksQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetItemStoreStocksQuery({
 *   variables: {
 *      itemId: // value for 'itemId'
 *   },
 * });
 */
export function useGetItemStoreStocksQuery(baseOptions: Apollo.QueryHookOptions<GetItemStoreStocksQuery, GetItemStoreStocksQueryVariables> & ({ variables: GetItemStoreStocksQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetItemStoreStocksQuery, GetItemStoreStocksQueryVariables>(GetItemStoreStocksDocument, options);
      }
export function useGetItemStoreStocksLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetItemStoreStocksQuery, GetItemStoreStocksQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetItemStoreStocksQuery, GetItemStoreStocksQueryVariables>(GetItemStoreStocksDocument, options);
        }
export function useGetItemStoreStocksSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetItemStoreStocksQuery, GetItemStoreStocksQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetItemStoreStocksQuery, GetItemStoreStocksQueryVariables>(GetItemStoreStocksDocument, options);
        }
export type GetItemStoreStocksQueryHookResult = ReturnType<typeof useGetItemStoreStocksQuery>;
export type GetItemStoreStocksLazyQueryHookResult = ReturnType<typeof useGetItemStoreStocksLazyQuery>;
export type GetItemStoreStocksSuspenseQueryHookResult = ReturnType<typeof useGetItemStoreStocksSuspenseQuery>;
export type GetItemStoreStocksQueryResult = Apollo.QueryResult<GetItemStoreStocksQuery, GetItemStoreStocksQueryVariables>;
export const GetItemBatchImportsDocument = gql`
    query getItemBatchImports($itemId: Float!) {
  getItemBatchImports(itemId: $itemId) {
    id
    importPrice
    batch
  }
}
    `;

/**
 * __useGetItemBatchImportsQuery__
 *
 * To run a query within a React component, call `useGetItemBatchImportsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetItemBatchImportsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetItemBatchImportsQuery({
 *   variables: {
 *      itemId: // value for 'itemId'
 *   },
 * });
 */
export function useGetItemBatchImportsQuery(baseOptions: Apollo.QueryHookOptions<GetItemBatchImportsQuery, GetItemBatchImportsQueryVariables> & ({ variables: GetItemBatchImportsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetItemBatchImportsQuery, GetItemBatchImportsQueryVariables>(GetItemBatchImportsDocument, options);
      }
export function useGetItemBatchImportsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetItemBatchImportsQuery, GetItemBatchImportsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetItemBatchImportsQuery, GetItemBatchImportsQueryVariables>(GetItemBatchImportsDocument, options);
        }
export function useGetItemBatchImportsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetItemBatchImportsQuery, GetItemBatchImportsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetItemBatchImportsQuery, GetItemBatchImportsQueryVariables>(GetItemBatchImportsDocument, options);
        }
export type GetItemBatchImportsQueryHookResult = ReturnType<typeof useGetItemBatchImportsQuery>;
export type GetItemBatchImportsLazyQueryHookResult = ReturnType<typeof useGetItemBatchImportsLazyQuery>;
export type GetItemBatchImportsSuspenseQueryHookResult = ReturnType<typeof useGetItemBatchImportsSuspenseQuery>;
export type GetItemBatchImportsQueryResult = Apollo.QueryResult<GetItemBatchImportsQuery, GetItemBatchImportsQueryVariables>;
export const MeDocument = gql`
    query Me {
  me {
    id
    firstname
    middlename
    lastname
    email
    phone
    image
    companyId
    company {
      id
      name
    }
    role {
      id
      name
    }
    employee {
      id
      status
      designation
      licenceNumber
      role {
        id
        name
      }
      department {
        id
        name
      }
      storeId
      store {
        id
        name
      }
    }
    permissions {
      id
      name
    }
  }
}
    `;

/**
 * __useMeQuery__
 *
 * To run a query within a React component, call `useMeQuery` and pass it any options that fit your needs.
 * When your component renders, `useMeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useMeQuery({
 *   variables: {
 *   },
 * });
 */
export function useMeQuery(baseOptions?: Apollo.QueryHookOptions<MeQuery, MeQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<MeQuery, MeQueryVariables>(MeDocument, options);
      }
export function useMeLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<MeQuery, MeQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<MeQuery, MeQueryVariables>(MeDocument, options);
        }
export function useMeSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<MeQuery, MeQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<MeQuery, MeQueryVariables>(MeDocument, options);
        }
export type MeQueryHookResult = ReturnType<typeof useMeQuery>;
export type MeLazyQueryHookResult = ReturnType<typeof useMeLazyQuery>;
export type MeSuspenseQueryHookResult = ReturnType<typeof useMeSuspenseQuery>;
export type MeQueryResult = Apollo.QueryResult<MeQuery, MeQueryVariables>;
export const GetPatientsDocument = gql`
    query getPatients {
  getPatients {
    id
    firstname
    lastname
    middlename
    phone
    email
    status
    dateOfBirth
    nationalId
    address {
      country
      city
      street
      ward
      district
    }
    religion
    gender
    fileNumber
    insuranceId
    insuranceUserId
    insuranceStatus
    insuranceProvider
    insuranceSchemeId
    insuranceCardNumber
    nextOfKinName
    nextOfKinPhone
    nextOfKinRelationship
  }
}
    `;

/**
 * __useGetPatientsQuery__
 *
 * To run a query within a React component, call `useGetPatientsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPatientsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPatientsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetPatientsQuery(baseOptions?: Apollo.QueryHookOptions<GetPatientsQuery, GetPatientsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPatientsQuery, GetPatientsQueryVariables>(GetPatientsDocument, options);
      }
export function useGetPatientsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPatientsQuery, GetPatientsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPatientsQuery, GetPatientsQueryVariables>(GetPatientsDocument, options);
        }
export function useGetPatientsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetPatientsQuery, GetPatientsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetPatientsQuery, GetPatientsQueryVariables>(GetPatientsDocument, options);
        }
export type GetPatientsQueryHookResult = ReturnType<typeof useGetPatientsQuery>;
export type GetPatientsLazyQueryHookResult = ReturnType<typeof useGetPatientsLazyQuery>;
export type GetPatientsSuspenseQueryHookResult = ReturnType<typeof useGetPatientsSuspenseQuery>;
export type GetPatientsQueryResult = Apollo.QueryResult<GetPatientsQuery, GetPatientsQueryVariables>;
export const GetPatientDocument = gql`
    query getPatient($id: Float!) {
  getPatient(id: $id) {
    id
    firstname
    lastname
    middlename
    phone
    email
    status
    dateOfBirth
    nationalId
    address {
      country
      city
      street
      ward
      district
    }
    religion
    gender
    fileNumber
    insuranceId
    insuranceUserId
    insuranceStatus
    insuranceProvider
    insuranceSchemeId
    insuranceCardNumber
    nextOfKinName
    nextOfKinPhone
    nextOfKinRelationship
  }
}
    `;

/**
 * __useGetPatientQuery__
 *
 * To run a query within a React component, call `useGetPatientQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPatientQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPatientQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetPatientQuery(baseOptions: Apollo.QueryHookOptions<GetPatientQuery, GetPatientQueryVariables> & ({ variables: GetPatientQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPatientQuery, GetPatientQueryVariables>(GetPatientDocument, options);
      }
export function useGetPatientLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPatientQuery, GetPatientQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPatientQuery, GetPatientQueryVariables>(GetPatientDocument, options);
        }
export function useGetPatientSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetPatientQuery, GetPatientQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetPatientQuery, GetPatientQueryVariables>(GetPatientDocument, options);
        }
export type GetPatientQueryHookResult = ReturnType<typeof useGetPatientQuery>;
export type GetPatientLazyQueryHookResult = ReturnType<typeof useGetPatientLazyQuery>;
export type GetPatientSuspenseQueryHookResult = ReturnType<typeof useGetPatientSuspenseQuery>;
export type GetPatientQueryResult = Apollo.QueryResult<GetPatientQuery, GetPatientQueryVariables>;
export const GetVisitsDocument = gql`
    query getVisits {
  getVisits {
    id
    status
    type
    reason
    consultation
    currentLocation
    clientId
    client {
      id
      firstname
      middlename
      lastname
      status
      email
      phone
      insuranceProvider
      insuranceId
      insuranceUserId
      insuranceStatus
      insuranceSchemeId
      insuranceCardNumber
      gender
      dateOfBirth
      fileNumber
      nationalId
      bloodGroup
      registererId
    }
    bills {
      id
      cleared
      amount
      paymentType
    }
    visitToClinics {
      id
      clinicId
    }
  }
}
    `;

/**
 * __useGetVisitsQuery__
 *
 * To run a query within a React component, call `useGetVisitsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetVisitsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetVisitsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetVisitsQuery(baseOptions?: Apollo.QueryHookOptions<GetVisitsQuery, GetVisitsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetVisitsQuery, GetVisitsQueryVariables>(GetVisitsDocument, options);
      }
export function useGetVisitsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetVisitsQuery, GetVisitsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetVisitsQuery, GetVisitsQueryVariables>(GetVisitsDocument, options);
        }
export function useGetVisitsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetVisitsQuery, GetVisitsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetVisitsQuery, GetVisitsQueryVariables>(GetVisitsDocument, options);
        }
export type GetVisitsQueryHookResult = ReturnType<typeof useGetVisitsQuery>;
export type GetVisitsLazyQueryHookResult = ReturnType<typeof useGetVisitsLazyQuery>;
export type GetVisitsSuspenseQueryHookResult = ReturnType<typeof useGetVisitsSuspenseQuery>;
export type GetVisitsQueryResult = Apollo.QueryResult<GetVisitsQuery, GetVisitsQueryVariables>;
export const GetPermissionsDocument = gql`
    query getPermissions {
  getPermissions {
    id
    name
    roles {
      id
      name
    }
  }
}
    `;

/**
 * __useGetPermissionsQuery__
 *
 * To run a query within a React component, call `useGetPermissionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetPermissionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetPermissionsQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetPermissionsQuery(baseOptions?: Apollo.QueryHookOptions<GetPermissionsQuery, GetPermissionsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetPermissionsQuery, GetPermissionsQueryVariables>(GetPermissionsDocument, options);
      }
export function useGetPermissionsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetPermissionsQuery, GetPermissionsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetPermissionsQuery, GetPermissionsQueryVariables>(GetPermissionsDocument, options);
        }
export function useGetPermissionsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetPermissionsQuery, GetPermissionsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetPermissionsQuery, GetPermissionsQueryVariables>(GetPermissionsDocument, options);
        }
export type GetPermissionsQueryHookResult = ReturnType<typeof useGetPermissionsQuery>;
export type GetPermissionsLazyQueryHookResult = ReturnType<typeof useGetPermissionsLazyQuery>;
export type GetPermissionsSuspenseQueryHookResult = ReturnType<typeof useGetPermissionsSuspenseQuery>;
export type GetPermissionsQueryResult = Apollo.QueryResult<GetPermissionsQuery, GetPermissionsQueryVariables>;
export const GetFeaturesDocument = gql`
    query getFeatures {
  getFeatures {
    id
    name
    companies {
      id
      name
      type
    }
  }
}
    `;

/**
 * __useGetFeaturesQuery__
 *
 * To run a query within a React component, call `useGetFeaturesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetFeaturesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetFeaturesQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetFeaturesQuery(baseOptions?: Apollo.QueryHookOptions<GetFeaturesQuery, GetFeaturesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetFeaturesQuery, GetFeaturesQueryVariables>(GetFeaturesDocument, options);
      }
export function useGetFeaturesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetFeaturesQuery, GetFeaturesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetFeaturesQuery, GetFeaturesQueryVariables>(GetFeaturesDocument, options);
        }
export function useGetFeaturesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetFeaturesQuery, GetFeaturesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetFeaturesQuery, GetFeaturesQueryVariables>(GetFeaturesDocument, options);
        }
export type GetFeaturesQueryHookResult = ReturnType<typeof useGetFeaturesQuery>;
export type GetFeaturesLazyQueryHookResult = ReturnType<typeof useGetFeaturesLazyQuery>;
export type GetFeaturesSuspenseQueryHookResult = ReturnType<typeof useGetFeaturesSuspenseQuery>;
export type GetFeaturesQueryResult = Apollo.QueryResult<GetFeaturesQuery, GetFeaturesQueryVariables>;
export const GetTypesDocument = gql`
    query getTypes {
  getTypes {
    id
    name
    description
    category {
      id
      name
    }
  }
}
    `;

/**
 * __useGetTypesQuery__
 *
 * To run a query within a React component, call `useGetTypesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTypesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTypesQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetTypesQuery(baseOptions?: Apollo.QueryHookOptions<GetTypesQuery, GetTypesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetTypesQuery, GetTypesQueryVariables>(GetTypesDocument, options);
      }
export function useGetTypesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetTypesQuery, GetTypesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetTypesQuery, GetTypesQueryVariables>(GetTypesDocument, options);
        }
export function useGetTypesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetTypesQuery, GetTypesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetTypesQuery, GetTypesQueryVariables>(GetTypesDocument, options);
        }
export type GetTypesQueryHookResult = ReturnType<typeof useGetTypesQuery>;
export type GetTypesLazyQueryHookResult = ReturnType<typeof useGetTypesLazyQuery>;
export type GetTypesSuspenseQueryHookResult = ReturnType<typeof useGetTypesSuspenseQuery>;
export type GetTypesQueryResult = Apollo.QueryResult<GetTypesQuery, GetTypesQueryVariables>;
export const GetTypeDocument = gql`
    query getType($id: Float!) {
  getType(id: $id) {
    id
    name
    description
    category {
      id
      name
    }
  }
}
    `;

/**
 * __useGetTypeQuery__
 *
 * To run a query within a React component, call `useGetTypeQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetTypeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetTypeQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetTypeQuery(baseOptions: Apollo.QueryHookOptions<GetTypeQuery, GetTypeQueryVariables> & ({ variables: GetTypeQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetTypeQuery, GetTypeQueryVariables>(GetTypeDocument, options);
      }
export function useGetTypeLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetTypeQuery, GetTypeQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetTypeQuery, GetTypeQueryVariables>(GetTypeDocument, options);
        }
export function useGetTypeSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetTypeQuery, GetTypeQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetTypeQuery, GetTypeQueryVariables>(GetTypeDocument, options);
        }
export type GetTypeQueryHookResult = ReturnType<typeof useGetTypeQuery>;
export type GetTypeLazyQueryHookResult = ReturnType<typeof useGetTypeLazyQuery>;
export type GetTypeSuspenseQueryHookResult = ReturnType<typeof useGetTypeSuspenseQuery>;
export type GetTypeQueryResult = Apollo.QueryResult<GetTypeQuery, GetTypeQueryVariables>;
export const GetRolesDocument = gql`
    query getRoles {
  getRoles {
    id
    sys
    name
  }
}
    `;

/**
 * __useGetRolesQuery__
 *
 * To run a query within a React component, call `useGetRolesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetRolesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetRolesQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetRolesQuery(baseOptions?: Apollo.QueryHookOptions<GetRolesQuery, GetRolesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetRolesQuery, GetRolesQueryVariables>(GetRolesDocument, options);
      }
export function useGetRolesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetRolesQuery, GetRolesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetRolesQuery, GetRolesQueryVariables>(GetRolesDocument, options);
        }
export function useGetRolesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetRolesQuery, GetRolesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetRolesQuery, GetRolesQueryVariables>(GetRolesDocument, options);
        }
export type GetRolesQueryHookResult = ReturnType<typeof useGetRolesQuery>;
export type GetRolesLazyQueryHookResult = ReturnType<typeof useGetRolesLazyQuery>;
export type GetRolesSuspenseQueryHookResult = ReturnType<typeof useGetRolesSuspenseQuery>;
export type GetRolesQueryResult = Apollo.QueryResult<GetRolesQuery, GetRolesQueryVariables>;
export const GetRoleDocument = gql`
    query getRole($name: String!) {
  getRole(name: $name) {
    id
    name
    permissions {
      id
      name
    }
  }
}
    `;

/**
 * __useGetRoleQuery__
 *
 * To run a query within a React component, call `useGetRoleQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetRoleQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetRoleQuery({
 *   variables: {
 *      name: // value for 'name'
 *   },
 * });
 */
export function useGetRoleQuery(baseOptions: Apollo.QueryHookOptions<GetRoleQuery, GetRoleQueryVariables> & ({ variables: GetRoleQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetRoleQuery, GetRoleQueryVariables>(GetRoleDocument, options);
      }
export function useGetRoleLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetRoleQuery, GetRoleQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetRoleQuery, GetRoleQueryVariables>(GetRoleDocument, options);
        }
export function useGetRoleSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetRoleQuery, GetRoleQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetRoleQuery, GetRoleQueryVariables>(GetRoleDocument, options);
        }
export type GetRoleQueryHookResult = ReturnType<typeof useGetRoleQuery>;
export type GetRoleLazyQueryHookResult = ReturnType<typeof useGetRoleLazyQuery>;
export type GetRoleSuspenseQueryHookResult = ReturnType<typeof useGetRoleSuspenseQuery>;
export type GetRoleQueryResult = Apollo.QueryResult<GetRoleQuery, GetRoleQueryVariables>;
export const GetAllCategoriesDocument = gql`
    query getAllCategories {
  getAllCategories {
    id
    name
  }
}
    `;

/**
 * __useGetAllCategoriesQuery__
 *
 * To run a query within a React component, call `useGetAllCategoriesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetAllCategoriesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetAllCategoriesQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetAllCategoriesQuery(baseOptions?: Apollo.QueryHookOptions<GetAllCategoriesQuery, GetAllCategoriesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetAllCategoriesQuery, GetAllCategoriesQueryVariables>(GetAllCategoriesDocument, options);
      }
export function useGetAllCategoriesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetAllCategoriesQuery, GetAllCategoriesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetAllCategoriesQuery, GetAllCategoriesQueryVariables>(GetAllCategoriesDocument, options);
        }
export function useGetAllCategoriesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetAllCategoriesQuery, GetAllCategoriesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetAllCategoriesQuery, GetAllCategoriesQueryVariables>(GetAllCategoriesDocument, options);
        }
export type GetAllCategoriesQueryHookResult = ReturnType<typeof useGetAllCategoriesQuery>;
export type GetAllCategoriesLazyQueryHookResult = ReturnType<typeof useGetAllCategoriesLazyQuery>;
export type GetAllCategoriesSuspenseQueryHookResult = ReturnType<typeof useGetAllCategoriesSuspenseQuery>;
export type GetAllCategoriesQueryResult = Apollo.QueryResult<GetAllCategoriesQuery, GetAllCategoriesQueryVariables>;
export const GetCategoriesDocument = gql`
    query getCategories($type: String!) {
  getCategories(type: $type) {
    id
    name
  }
}
    `;

/**
 * __useGetCategoriesQuery__
 *
 * To run a query within a React component, call `useGetCategoriesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetCategoriesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetCategoriesQuery({
 *   variables: {
 *      type: // value for 'type'
 *   },
 * });
 */
export function useGetCategoriesQuery(baseOptions: Apollo.QueryHookOptions<GetCategoriesQuery, GetCategoriesQueryVariables> & ({ variables: GetCategoriesQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetCategoriesQuery, GetCategoriesQueryVariables>(GetCategoriesDocument, options);
      }
export function useGetCategoriesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetCategoriesQuery, GetCategoriesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetCategoriesQuery, GetCategoriesQueryVariables>(GetCategoriesDocument, options);
        }
export function useGetCategoriesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetCategoriesQuery, GetCategoriesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetCategoriesQuery, GetCategoriesQueryVariables>(GetCategoriesDocument, options);
        }
export type GetCategoriesQueryHookResult = ReturnType<typeof useGetCategoriesQuery>;
export type GetCategoriesLazyQueryHookResult = ReturnType<typeof useGetCategoriesLazyQuery>;
export type GetCategoriesSuspenseQueryHookResult = ReturnType<typeof useGetCategoriesSuspenseQuery>;
export type GetCategoriesQueryResult = Apollo.QueryResult<GetCategoriesQuery, GetCategoriesQueryVariables>;
export const ExpensesDocument = gql`
    query expenses($filter: ExpenseFilterInput) {
  expenses(filter: $filter) {
    id
    expenseDate
    authorizer {
      id
    }
    requester {
      id
    }
    assetType
    type
    status
    title
    details
    amount
    createdAt
    updatedAt
  }
}
    `;

/**
 * __useExpensesQuery__
 *
 * To run a query within a React component, call `useExpensesQuery` and pass it any options that fit your needs.
 * When your component renders, `useExpensesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useExpensesQuery({
 *   variables: {
 *      filter: // value for 'filter'
 *   },
 * });
 */
export function useExpensesQuery(baseOptions?: Apollo.QueryHookOptions<ExpensesQuery, ExpensesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<ExpensesQuery, ExpensesQueryVariables>(ExpensesDocument, options);
      }
export function useExpensesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<ExpensesQuery, ExpensesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<ExpensesQuery, ExpensesQueryVariables>(ExpensesDocument, options);
        }
export function useExpensesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<ExpensesQuery, ExpensesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<ExpensesQuery, ExpensesQueryVariables>(ExpensesDocument, options);
        }
export type ExpensesQueryHookResult = ReturnType<typeof useExpensesQuery>;
export type ExpensesLazyQueryHookResult = ReturnType<typeof useExpensesLazyQuery>;
export type ExpensesSuspenseQueryHookResult = ReturnType<typeof useExpensesSuspenseQuery>;
export type ExpensesQueryResult = Apollo.QueryResult<ExpensesQuery, ExpensesQueryVariables>;
export const GetExpensesDocument = gql`
    query getExpenses {
  getExpenses {
    id
    expenseDate
    authorizer {
      id
    }
    requester {
      id
    }
    assetType
    type
    status
    title
    details
    amount
    createdAt
    updatedAt
  }
}
    `;

/**
 * __useGetExpensesQuery__
 *
 * To run a query within a React component, call `useGetExpensesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetExpensesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetExpensesQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetExpensesQuery(baseOptions?: Apollo.QueryHookOptions<GetExpensesQuery, GetExpensesQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetExpensesQuery, GetExpensesQueryVariables>(GetExpensesDocument, options);
      }
export function useGetExpensesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetExpensesQuery, GetExpensesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetExpensesQuery, GetExpensesQueryVariables>(GetExpensesDocument, options);
        }
export function useGetExpensesSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetExpensesQuery, GetExpensesQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetExpensesQuery, GetExpensesQueryVariables>(GetExpensesDocument, options);
        }
export type GetExpensesQueryHookResult = ReturnType<typeof useGetExpensesQuery>;
export type GetExpensesLazyQueryHookResult = ReturnType<typeof useGetExpensesLazyQuery>;
export type GetExpensesSuspenseQueryHookResult = ReturnType<typeof useGetExpensesSuspenseQuery>;
export type GetExpensesQueryResult = Apollo.QueryResult<GetExpensesQuery, GetExpensesQueryVariables>;
export const GetExpenseDocument = gql`
    query getExpense($id: Float!) {
  getExpense(id: $id) {
    id
    expenseDate
    authorizer {
      id
    }
    requester {
      id
    }
    assetType
    type
    status
    title
    details
    amount
    createdAt
    updatedAt
  }
}
    `;

/**
 * __useGetExpenseQuery__
 *
 * To run a query within a React component, call `useGetExpenseQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetExpenseQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetExpenseQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetExpenseQuery(baseOptions: Apollo.QueryHookOptions<GetExpenseQuery, GetExpenseQueryVariables> & ({ variables: GetExpenseQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetExpenseQuery, GetExpenseQueryVariables>(GetExpenseDocument, options);
      }
export function useGetExpenseLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetExpenseQuery, GetExpenseQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetExpenseQuery, GetExpenseQueryVariables>(GetExpenseDocument, options);
        }
export function useGetExpenseSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetExpenseQuery, GetExpenseQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetExpenseQuery, GetExpenseQueryVariables>(GetExpenseDocument, options);
        }
export type GetExpenseQueryHookResult = ReturnType<typeof useGetExpenseQuery>;
export type GetExpenseLazyQueryHookResult = ReturnType<typeof useGetExpenseLazyQuery>;
export type GetExpenseSuspenseQueryHookResult = ReturnType<typeof useGetExpenseSuspenseQuery>;
export type GetExpenseQueryResult = Apollo.QueryResult<GetExpenseQuery, GetExpenseQueryVariables>;
export const GetLatestPaymentDocument = gql`
    query getLatestPayment {
  getLatestPayment {
    id
    status
    startDate
    endDate
    packageName
    billingCycle
    amount
  }
}
    `;

/**
 * __useGetLatestPaymentQuery__
 *
 * To run a query within a React component, call `useGetLatestPaymentQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetLatestPaymentQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetLatestPaymentQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetLatestPaymentQuery(baseOptions?: Apollo.QueryHookOptions<GetLatestPaymentQuery, GetLatestPaymentQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetLatestPaymentQuery, GetLatestPaymentQueryVariables>(GetLatestPaymentDocument, options);
      }
export function useGetLatestPaymentLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetLatestPaymentQuery, GetLatestPaymentQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetLatestPaymentQuery, GetLatestPaymentQueryVariables>(GetLatestPaymentDocument, options);
        }
export function useGetLatestPaymentSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetLatestPaymentQuery, GetLatestPaymentQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetLatestPaymentQuery, GetLatestPaymentQueryVariables>(GetLatestPaymentDocument, options);
        }
export type GetLatestPaymentQueryHookResult = ReturnType<typeof useGetLatestPaymentQuery>;
export type GetLatestPaymentLazyQueryHookResult = ReturnType<typeof useGetLatestPaymentLazyQuery>;
export type GetLatestPaymentSuspenseQueryHookResult = ReturnType<typeof useGetLatestPaymentSuspenseQuery>;
export type GetLatestPaymentQueryResult = Apollo.QueryResult<GetLatestPaymentQuery, GetLatestPaymentQueryVariables>;
export const GetActivePaymentDocument = gql`
    query getActivePayment($companyId: Float!) {
  getActivePayment(companyId: $companyId) {
    id
    status
    startDate
    endDate
    packageName
    billingCycle
    amount
  }
}
    `;

/**
 * __useGetActivePaymentQuery__
 *
 * To run a query within a React component, call `useGetActivePaymentQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetActivePaymentQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetActivePaymentQuery({
 *   variables: {
 *      companyId: // value for 'companyId'
 *   },
 * });
 */
export function useGetActivePaymentQuery(baseOptions: Apollo.QueryHookOptions<GetActivePaymentQuery, GetActivePaymentQueryVariables> & ({ variables: GetActivePaymentQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetActivePaymentQuery, GetActivePaymentQueryVariables>(GetActivePaymentDocument, options);
      }
export function useGetActivePaymentLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetActivePaymentQuery, GetActivePaymentQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetActivePaymentQuery, GetActivePaymentQueryVariables>(GetActivePaymentDocument, options);
        }
export function useGetActivePaymentSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<GetActivePaymentQuery, GetActivePaymentQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<GetActivePaymentQuery, GetActivePaymentQueryVariables>(GetActivePaymentDocument, options);
        }
export type GetActivePaymentQueryHookResult = ReturnType<typeof useGetActivePaymentQuery>;
export type GetActivePaymentLazyQueryHookResult = ReturnType<typeof useGetActivePaymentLazyQuery>;
export type GetActivePaymentSuspenseQueryHookResult = ReturnType<typeof useGetActivePaymentSuspenseQuery>;
export type GetActivePaymentQueryResult = Apollo.QueryResult<GetActivePaymentQuery, GetActivePaymentQueryVariables>;
export const PaymentsDocument = gql`
    query payments {
  payments {
    id
    status
    startDate
    endDate
    packageName
    billingCycle
    amount
  }
}
    `;

/**
 * __usePaymentsQuery__
 *
 * To run a query within a React component, call `usePaymentsQuery` and pass it any options that fit your needs.
 * When your component renders, `usePaymentsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePaymentsQuery({
 *   variables: {
 *   },
 * });
 */
export function usePaymentsQuery(baseOptions?: Apollo.QueryHookOptions<PaymentsQuery, PaymentsQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PaymentsQuery, PaymentsQueryVariables>(PaymentsDocument, options);
      }
export function usePaymentsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PaymentsQuery, PaymentsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PaymentsQuery, PaymentsQueryVariables>(PaymentsDocument, options);
        }
export function usePaymentsSuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<PaymentsQuery, PaymentsQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PaymentsQuery, PaymentsQueryVariables>(PaymentsDocument, options);
        }
export type PaymentsQueryHookResult = ReturnType<typeof usePaymentsQuery>;
export type PaymentsLazyQueryHookResult = ReturnType<typeof usePaymentsLazyQuery>;
export type PaymentsSuspenseQueryHookResult = ReturnType<typeof usePaymentsSuspenseQuery>;
export type PaymentsQueryResult = Apollo.QueryResult<PaymentsQuery, PaymentsQueryVariables>;