import React, { useState } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from "react-native";
import {
  Text,
  ActivityIndicator,
  Chip,
  Avatar,
  Surface,
  Button,
  Portal,
  Modal,
  Divider,
} from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useRouter, useLocalSearchParams } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import {
  useGetCompanyQuery,
  useChangePaymentStatusMutation,
  PaymentStatus,
} from "../../../../generated/graphql";
import { formatCurrency, formatDate } from "@/constants/functions";

export default function CompanyDetailScreen() {
  const router = useRouter();
  const { id } = useLocalSearchParams();
  const companyId = parseInt(id as string);

  const [modalVisible, setModalVisible] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<any>(null);

  const { data, loading, error, refetch } = useGetCompanyQuery({
    variables: { id: companyId },
    fetchPolicy: "network-only",
  });

  const [changePaymentStatus, { loading: changingStatus }] =
    useChangePaymentStatusMutation();

  const company = data?.getCompany;

  const getPaymentStatusColor = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.Active:
        return "#10B981";
      case PaymentStatus.Pending:
        return "#F59E0B";
      case PaymentStatus.Expired:
        return "#EF4444";
      case PaymentStatus.Trial:
        return "#8B5CF6";
      default:
        return "#6B7280";
    }
  };

  const getPaymentStatusIcon = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.Active:
        return "check-circle";
      case PaymentStatus.Pending:
        return "clock-outline";
      case PaymentStatus.Expired:
        return "alert-circle";
      case PaymentStatus.Trial:
        return "star-outline";
      default:
        return "help-circle";
    }
  };

  const handleChangePaymentStatus = async (
    paymentId: number,
    newStatus: string
  ) => {
    try {
      const result = await changePaymentStatus({
        variables: {
          id: paymentId,
          companyId,
          status: newStatus,
        },
      });

      if (result.data?.changePaymentStatus.status) {
        Alert.alert("Success", "Payment status updated successfully");
        refetch();
        setModalVisible(false);
        setSelectedPayment(null);
      } else {
        const error = result.data?.changePaymentStatus.error;
        Alert.alert(
          "Error",
          error?.message || "Failed to update payment status"
        );
      }
    } catch (error) {
      console.error("Error changing payment status:", error);
      Alert.alert("Error", "Failed to update payment status");
    }
  };

  const openPaymentModal = (payment: any) => {
    setSelectedPayment(payment);
    setModalVisible(true);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1F72A1" />
        <Text style={styles.loadingText}>Loading company details...</Text>
      </View>
    );
  }

  if (error || !company) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="alert-circle" size={48} color="#EF4444" />
        <Text style={styles.errorText}>Failed to load company details</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const employees = company.employees || [];
  const features = company.features || [];
  const payments = company.payments || [];

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#1F72A1", "#3B82F6"]}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <MaterialCommunityIcons
              name="arrow-left"
              size={24}
              color="#FFFFFF"
            />
          </TouchableOpacity>
          <View style={styles.headerInfo}>
            <Text style={styles.headerTitle}>{company.name}</Text>
            <Text style={styles.headerSubtitle}>{company.location}</Text>
          </View>
        </View>
      </LinearGradient>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Company Info Card */}
        <Surface style={styles.card} elevation={4}>
          <LinearGradient
            colors={["#FFFFFF", "#F8FAFC"]}
            style={styles.cardGradient}
          >
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons
                name="office-building"
                size={24}
                color="#1F72A1"
              />
              <Text style={styles.cardTitle}>Company Information</Text>
            </View>
            <View style={styles.infoGrid}>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Type</Text>
                <Text style={styles.infoValue}>{company.type}</Text>
              </View>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>TIN Number</Text>
                <Text style={styles.infoValue}>{company.tinNumber}</Text>
              </View>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Registration Number</Text>
                <Text style={styles.infoValue}>
                  {company.registrationNumber}
                </Text>
              </View>
              <View style={styles.infoItem}>
                <Text style={styles.infoLabel}>Location</Text>
                <Text style={styles.infoValue}>{company.location}</Text>
              </View>
            </View>
          </LinearGradient>
        </Surface>

        {/* Payments Card */}
        <Surface style={styles.card} elevation={4}>
          <LinearGradient
            colors={["#FFFFFF", "#F8FAFC"]}
            style={styles.cardGradient}
          >
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons
                name="credit-card"
                size={24}
                color="#1F72A1"
              />
              <Text style={styles.cardTitle}>
                Payment History ({payments.length})
              </Text>
            </View>
            {payments.length > 0 ? (
              payments.map((payment) => (
                <View key={payment.id} style={styles.paymentItem}>
                  <View style={styles.paymentHeader}>
                    <View style={styles.paymentInfo}>
                      <Text style={styles.paymentPackage}>
                        {payment.packageName}
                      </Text>
                      <Text style={styles.paymentAmount}>
                        {formatCurrency(payment.amount)} /{" "}
                        {payment.billingCycle}
                      </Text>
                    </View>
                    <Chip
                      icon={() => (
                        <MaterialCommunityIcons
                          name={getPaymentStatusIcon(payment.status)}
                          size={16}
                          color="#FFFFFF"
                        />
                      )}
                      style={[
                        styles.statusChip,
                        {
                          backgroundColor: getPaymentStatusColor(
                            payment.status
                          ),
                        },
                      ]}
                      textStyle={styles.statusChipText}
                    >
                      {payment.status}
                    </Chip>
                  </View>
                  <Text style={styles.paymentPeriod}>
                    {formatDate(payment.startDate)} -{" "}
                    {formatDate(payment.endDate)}
                  </Text>
                  {payment.status === PaymentStatus.Pending && (
                    <Button
                      mode="contained"
                      onPress={() => openPaymentModal(payment)}
                      style={styles.changeStatusButton}
                      contentStyle={styles.buttonContent}
                    >
                      Change Status
                    </Button>
                  )}
                </View>
              ))
            ) : (
              <Text style={styles.emptyText}>No payment history</Text>
            )}
          </LinearGradient>
        </Surface>
        {/* Employees Card */}
        <Surface style={styles.card} elevation={4}>
          <LinearGradient
            colors={["#FFFFFF", "#F8FAFC"]}
            style={styles.cardGradient}
          >
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons
                name="account-group"
                size={24}
                color="#1F72A1"
              />
              <Text style={styles.cardTitle}>
                Employees ({employees.length})
              </Text>
            </View>
            {employees.length > 0 ? (
              employees.map((employee, index) => (
                <View key={employee.id} style={styles.employeeItem}>
                  <Avatar.Text
                    size={40}
                    label={`E${employee.id}`}
                    style={styles.employeeAvatar}
                    labelStyle={styles.avatarLabel}
                  />
                  <View style={styles.employeeInfo}>
                    <Text style={styles.employeeName}>
                      Employee #{employee.id}
                    </Text>
                    <Text style={styles.employeeRole}>
                      {employee.role.name}
                    </Text>
                  </View>
                </View>
              ))
            ) : (
              <Text style={styles.emptyText}>No employees registered</Text>
            )}
          </LinearGradient>
        </Surface>

        {/* Features Card */}
        <Surface style={styles.card} elevation={4}>
          <LinearGradient
            colors={["#FFFFFF", "#F8FAFC"]}
            style={styles.cardGradient}
          >
            <View style={styles.cardHeader}>
              <MaterialCommunityIcons
                name="feature-search"
                size={24}
                color="#1F72A1"
              />
              <Text style={styles.cardTitle}>Features ({features.length})</Text>
            </View>
            {features.length > 0 ? (
              <View style={styles.featuresGrid}>
                {features.map((feature) => (
                  <Chip
                    key={feature.id}
                    style={styles.featureChip}
                    textStyle={styles.featureChipText}
                  >
                    {feature.name}
                  </Chip>
                ))}
              </View>
            ) : (
              <Text style={styles.emptyText}>No features assigned</Text>
            )}
          </LinearGradient>
        </Surface>
      </ScrollView>

      {/* Payment Status Modal */}
      <Portal>
        <Modal
          visible={modalVisible}
          onDismiss={() => setModalVisible(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <Text style={styles.modalTitle}>Change Payment Status</Text>
          <Text style={styles.modalSubtitle}>
            {selectedPayment?.packageName} -{" "}
            {formatCurrency(selectedPayment?.amount || 0)}
          </Text>
          <Divider style={styles.modalDivider} />

          <View style={styles.modalButtons}>
            <Button
              mode="contained"
              onPress={() =>
                handleChangePaymentStatus(selectedPayment?.id, "active")
              }
              loading={changingStatus}
              style={[styles.modalButton, { backgroundColor: "#10B981" }]}
              contentStyle={styles.buttonContent}
            >
              Approve
            </Button>
            <Button
              mode="contained"
              onPress={() =>
                handleChangePaymentStatus(selectedPayment?.id, "expired")
              }
              loading={changingStatus}
              style={[styles.modalButton, { backgroundColor: "#EF4444" }]}
              contentStyle={styles.buttonContent}
            >
              Reject
            </Button>
          </View>

          <Button
            mode="text"
            onPress={() => setModalVisible(false)}
            style={styles.cancelButton}
          >
            Cancel
          </Button>
        </Modal>
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F1F5F9",
  },
  header: {
    paddingTop: 10,
    paddingBottom: 10,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 16,
    padding: 8,
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: "#E2E8F0",
    opacity: 0.9,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  card: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: "#1F72A1",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  cardGradient: {
    padding: 20,
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1E293B",
    marginLeft: 12,
  },
  infoGrid: {
    gap: 12,
  },
  infoItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#E2E8F0",
  },
  infoLabel: {
    fontSize: 14,
    color: "#64748B",
    fontWeight: "500",
  },
  infoValue: {
    fontSize: 14,
    color: "#1E293B",
    fontWeight: "600",
    flex: 1,
    textAlign: "right",
  },
  employeeItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#E2E8F0",
  },
  employeeAvatar: {
    backgroundColor: "#1F72A1",
    marginRight: 12,
  },
  avatarLabel: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  employeeInfo: {
    flex: 1,
  },
  employeeName: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1E293B",
    marginBottom: 2,
  },
  employeeRole: {
    fontSize: 14,
    color: "#64748B",
  },
  featuresGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  featureChip: {
    backgroundColor: "#E0F2FE",
    marginBottom: 4,
  },
  featureChipText: {
    color: "#0369A1",
    fontSize: 12,
  },
  paymentItem: {
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#E2E8F0",
  },
  paymentHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  paymentInfo: {
    flex: 1,
    marginRight: 12,
  },
  paymentPackage: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1E293B",
    marginBottom: 4,
  },
  paymentAmount: {
    fontSize: 14,
    color: "#64748B",
    fontWeight: "500",
  },
  paymentPeriod: {
    fontSize: 12,
    color: "#94A3B8",
    marginBottom: 12,
  },
  statusChip: {
    borderRadius: 20,
  },
  statusChipText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
  changeStatusButton: {
    backgroundColor: "#1F72A1",
    borderRadius: 8,
    alignSelf: "flex-start",
  },
  buttonContent: {
    paddingVertical: 4,
  },
  emptyText: {
    fontSize: 14,
    color: "#94A3B8",
    textAlign: "center",
    fontStyle: "italic",
    paddingVertical: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F1F5F9",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#64748B",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F1F5F9",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: "#EF4444",
    marginTop: 16,
    marginBottom: 24,
    textAlign: "center",
  },
  retryButton: {
    backgroundColor: "#1F72A1",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  modalContainer: {
    backgroundColor: "#FFFFFF",
    margin: 20,
    borderRadius: 16,
    padding: 24,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#1E293B",
    marginBottom: 8,
    textAlign: "center",
  },
  modalSubtitle: {
    fontSize: 14,
    color: "#64748B",
    textAlign: "center",
    marginBottom: 16,
  },
  modalDivider: {
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 16,
  },
  modalButton: {
    flex: 1,
    borderRadius: 8,
  },
  cancelButton: {
    alignSelf: "center",
  },
});
