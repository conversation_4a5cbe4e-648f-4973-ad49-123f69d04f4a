import { StyleSheet } from "react-native";
import { Appbar, Text } from "react-native-paper";
import { useDrawer } from "../contexts/DrawerContext";
import { LinearGradient } from "expo-linear-gradient";

export function TopNavigation() {
  const { setIsDrawerOpen } = useDrawer();

  const handleMenuPress = () => {
    setIsDrawerOpen(true);
  };

  return (
    <LinearGradient
      colors={["rgb(31, 114, 161)", "rgba(31, 114, 161, 0.8)"]}
      style={styles.header}
    >
      <Appbar.Header style={styles.appbar}>
        <Appbar.Action
          icon="menu"
          color="#FFFFFF"
          onPress={handleMenuPress}
          style={styles.menuButton}
        />
        <Text style={styles.companyName}>Talisia</Text>
      </Appbar.Header>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  header: {
    padding: 24,
    paddingTop: 10,
    paddingBottom: 20,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    zIndex: 1,
  },
  appbar: {
    backgroundColor: "transparent",
    elevation: 0,
    marginLeft: -8,
    marginTop: -8,
  },
  menuButton: {
    margin: 0,
  },
  companyName: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#ffffff",
    marginBottom: 8,
    marginTop: 8,
    marginLeft: 8,
  },
  welcome: {
    fontSize: 20,
    color: "#f8f9fa",
    fontWeight: "500",
  },
});
