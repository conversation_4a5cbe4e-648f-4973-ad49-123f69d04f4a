import { View, StyleSheet, Text } from "react-native";
import { useTheme } from "react-native-paper";
import { LinearGradient } from "expo-linear-gradient";
import { MaterialCommunityIcons } from "@expo/vector-icons";

export function Footer() {
  const theme = useTheme();
  const currentYear = new Date().getFullYear();

  return (
    <LinearGradient
      colors={["#1F72A1", "#3B82F6"]}
      style={styles.container}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
    >
      <View style={styles.content}>
        <View style={styles.brandSection}>
          <MaterialCommunityIcons
            name="shield-check"
            size={20}
            color="#FFFFFF"
            style={styles.brandIcon}
          />
          <Text style={styles.brandText}>Talisia</Text>
        </View>

        <View style={styles.divider} />

        <View style={styles.infoSection}>
          <Text style={styles.copyrightText}>
            © {currentYear} All rights reserved
          </Text>
          <View style={styles.poweredBySection}>
            <MaterialCommunityIcons
              name="lightning-bolt"
              size={14}
              color="#E2E8F0"
              style={styles.poweredIcon}
            />
            <Text style={styles.poweredBy}>Powered by JECS</Text>
          </View>
        </View>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 20,
    paddingHorizontal: 20,
    shadowColor: "#1F72A1",
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  content: {
    alignItems: "center",
    justifyContent: "center",
  },
  brandSection: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  brandIcon: {
    marginRight: 8,
  },
  brandText: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "bold",
    letterSpacing: 0.5,
  },
  divider: {
    width: 60,
    height: 1,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    marginBottom: 12,
  },
  infoSection: {
    alignItems: "center",
  },
  copyrightText: {
    color: "#E2E8F0",
    fontSize: 12,
    fontWeight: "500",
    marginBottom: 6,
    letterSpacing: 0.3,
  },
  poweredBySection: {
    flexDirection: "row",
    alignItems: "center",
  },
  poweredIcon: {
    marginRight: 4,
  },
  poweredBy: {
    color: "#E2E8F0",
    fontSize: 11,
    fontWeight: "400",
    fontStyle: "italic",
    opacity: 0.8,
  },
});
