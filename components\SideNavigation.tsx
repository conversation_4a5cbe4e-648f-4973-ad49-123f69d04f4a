import { View, StyleSheet, Platform, StatusBar, Image } from "react-native";
import { Drawer, useTheme } from "react-native-paper";
import { useRouter, usePathname } from "expo-router";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useApolloClient } from "@apollo/client";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useDrawer } from "@/contexts/DrawerContext";
import { useLogoutMutation } from "../generated/graphql";

interface IconProps {
  color: string;
  size: number;
}

export function SideNavigation() {
  const router = useRouter();
  const pathname = usePathname();
  const client = useApolloClient();
  const theme = useTheme();
  const { setIsDrawerOpen } = useDrawer();

  const handleNavigation = (path: string) => {
    setIsDrawerOpen(false);
    router.push(path as any);
  };

  const [logout] = useLogoutMutation();

  const handleLogout = async () => {
    try {
      await logout();
      setIsDrawerOpen(false);
      await AsyncStorage.removeItem("token");
      await client.resetStore();

      // Navigate immediately
      router.replace("/login");
    } catch (err) {
      console.error("Logout error:", err);
      // Ensure navigation happens even if there's an error
      router.replace("/login");
    }
  };

  const drawerTheme = {
    ...theme,
    colors: {
      ...theme.colors,
      text: "#374151", // Dark gray for text
      primary: "#3B82F6", // Blue for active items
      onSurface: "#374151", // Dark gray for inactive items
      onSurfaceVariant: "#6B7280", // Medium gray for subtle text
      surface: "#FFFFFF", // White background
      elevation: {
        level1: "#FFFFFF", // White for elevation
      },
    },
  };

  return (
    <View style={[styles.container, { backgroundColor: "#F9FAFB" }]}>
      <View style={styles.innerContainer}>
        <View style={styles.header}>
          <Image
            source={require("../assets/images/logo.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <Drawer.Section style={styles.drawerSection} theme={drawerTheme}>
          <Drawer.Item
            icon={({ color, size }: IconProps) => (
              <MaterialCommunityIcons
                name="view-dashboard"
                size={size}
                color={pathname === "/(tabs)/dashboard" ? "#3B82F6" : "#6B7280"}
              />
            )}
            label="Dashboard"
            onPress={() => handleNavigation("/(tabs)/dashboard")}
            active={pathname === "/(tabs)/dashboard"}
          />
          <Drawer.Item
            icon={({ color, size }: IconProps) => (
              <MaterialCommunityIcons
                name="cash-register"
                size={size}
                color={pathname === "/(tabs)/sales" ? "#3B82F6" : "#6B7280"}
              />
            )}
            label="Sales"
            onPress={() => handleNavigation("/(tabs)/sales")}
            active={pathname === "/(tabs)/sales"}
          />
          <Drawer.Item
            icon={({ color, size }: IconProps) => (
              <MaterialCommunityIcons
                name="package-variant"
                size={size}
                color={pathname === "/(tabs)/inventory" ? "#3B82F6" : "#6B7280"}
              />
            )}
            label="Inventory"
            onPress={() => handleNavigation("/(tabs)/inventory")}
            active={pathname === "/(tabs)/inventory"}
          />
          <Drawer.Item
            icon={({ color, size }: IconProps) => (
              <MaterialCommunityIcons
                name="account-group"
                size={size}
                color={pathname === "/(tabs)/employees" ? "#3B82F6" : "#6B7280"}
              />
            )}
            label="Employees"
            onPress={() => handleNavigation("/(tabs)/employees")}
            active={pathname === "/(tabs)/employees"}
          />
          <Drawer.Item
            icon={({ color, size }: IconProps) => (
              <MaterialCommunityIcons
                name="shield-account"
                size={size}
                color={pathname === "/(tabs)/admin" ? "#3B82F6" : "#6B7280"}
              />
            )}
            label="Admin"
            onPress={() => handleNavigation("/(tabs)/admin")}
            active={pathname === "/(tabs)/admin"}
          />
          <Drawer.Item
            icon={({ color, size }: IconProps) => (
              <MaterialCommunityIcons
                name="cog"
                size={size}
                color={pathname === "/(tabs)/settings" ? "#3B82F6" : "#6B7280"}
              />
            )}
            label="Settings"
            onPress={() => handleNavigation("/(tabs)/settings")}
            active={pathname === "/(tabs)/settings"}
          />
        </Drawer.Section>

        <Drawer.Section style={styles.logoutSection} theme={drawerTheme}>
          <Drawer.Item
            icon={({ color, size }: IconProps) => (
              <MaterialCommunityIcons
                name="logout"
                size={size}
                color="#EF4444" // Red for logout
              />
            )}
            label="Logout"
            onPress={handleLogout}
            style={styles.logoutItem}
          />
        </Drawer.Section>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: Platform.OS === "android" ? StatusBar.currentHeight ?? 32 : 48,
    backgroundColor: "#F9FAFB", // Light gray background
    borderColor: "none",
  },
  innerContainer: {
    flex: 1,
    backgroundColor: "#FFFFFF", // White container
    borderRadius: 12,
    margin: 8,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    borderColor: "none",
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#E5E7EB", // Light gray border
    alignItems: "center",
    justifyContent: "center",
  },
  logo: {
    width: 120,
    height: 40,
    tintColor: "#1F2937", // Dark color for logo
  },
  drawerSection: {
    paddingTop: 16,
    paddingBottom: 8,
    borderRadius: 12,
    borderColor: "none",
  },
  logoutSection: {
    marginTop: "auto",
    borderRadius: 12,
    borderColor: "none",
    backgroundColor: "#F9FAFB", // Light gray background
  },
  logoutItem: {
    marginTop: 4,

    borderColor: "none",
  },
});
