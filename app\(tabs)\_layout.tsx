import { Stack } from "expo-router";
import { Layout } from "@/components/Layout";

export default function TabLayout() {
  return (
    <Layout>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="dashboard" />
        <Stack.Screen name="admin" />
        <Stack.Screen name="sales" />
        <Stack.Screen name="inventory" />
        <Stack.Screen name="employees" />
        <Stack.Screen name="settings" />
      </Stack>
    </Layout>
  );
}
