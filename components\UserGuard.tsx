import React from "react";
import { View, StyleSheet } from "react-native";
import { Text, ActivityIndicator } from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useMeContext } from "../contexts/MeContext";
import { useEffect } from "react";

interface UserGuardProps {
  children: React.ReactNode;
}

export function UserGuard({ children }: UserGuardProps) {
  const { me, loading } = useMeContext();
  const router = useRouter();

  useEffect(() => {
    // If user is loaded and is an admin, redirect to admin dashboard
    if (!loading && me && me.company?.id === 0) {
      router.replace("/(tabs)/admin");
    }
  }, [me, loading, router]);

  // Show loading while checking user
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1F72A1" />
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }

  // Show access message if admin trying to access user routes
  if (me && me.company?.id === 0) {
    return (
      <View style={styles.accessDeniedContainer}>
        <MaterialCommunityIcons name="shield-account" size={64} color="#1F72A1" />
        <Text style={styles.accessDeniedTitle}>Admin Account</Text>
        <Text style={styles.accessDeniedText}>
          You are logged in as an administrator.
        </Text>
        <Text style={styles.accessDeniedSubtext}>
          Please use the Admin Dashboard to manage the system.
        </Text>
      </View>
    );
  }

  // Render children if user is not admin
  return <>{children}</>;
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F1F5F9",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#64748B",
  },
  accessDeniedContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F1F5F9",
    padding: 20,
  },
  accessDeniedTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#1F72A1",
    marginTop: 16,
    marginBottom: 8,
  },
  accessDeniedText: {
    fontSize: 16,
    color: "#64748B",
    textAlign: "center",
    marginBottom: 8,
  },
  accessDeniedSubtext: {
    fontSize: 14,
    color: "#94A3B8",
    textAlign: "center",
  },
});
