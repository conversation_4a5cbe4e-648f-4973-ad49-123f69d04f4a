import { View, StyleSheet } from "react-native";
import { Card, Text } from "react-native-paper";
import { Image } from "expo-image";
import React from "react";
import { Inventory, Item } from "@/generated/graphql";

export function SalesInsightList({
  salesData,
  inventoryData,
  searchQuery,
  showLeastSold,
}: {
  salesData: Inventory[];
  inventoryData: Item[];
  searchQuery: string;
  showLeastSold: boolean;
}) {
  const itemSalesMap = React.useMemo(() => {
    const salesMap = new Map();

    // Calculate total sales for each item in the last month
    salesData.forEach((sale) => {
      sale.transfers.forEach((transfer) => {
        const currentCount = salesMap.get(transfer.itemId) || 0;
        salesMap.set(transfer.itemId, currentCount + transfer.quantity);
      });
    });

    return salesMap;
  }, [salesData]);

  const sortedItems = React.useMemo(() => {
    let items = [...inventoryData];

    // Filter by search if needed (case-insensitive)
    if (searchQuery) {
      items = items.filter((item) =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (showLeastSold) {
      // Filter items that haven't been sold in the last month
      items = items.filter((item) => !itemSalesMap.has(item.id));
    } else {
      // Filter out items that haven't been sold in the last month
      items = items.filter((item) => itemSalesMap.has(item.id));
      // Sort by sales count (highest to lowest)
      items.sort((a, b) => {
        const salesA = itemSalesMap.get(a.id) || 0;
        const salesB = itemSalesMap.get(b.id) || 0;
        return salesB - salesA;
      });
    }

    return items;
  }, [inventoryData, itemSalesMap, searchQuery, showLeastSold]);

  return (
    <>
      {sortedItems.map((item) => {
        const salesCount = itemSalesMap.get(item.id) || 0;

        return (
          <Card key={item.id} style={styles.productCard}>
            <Card.Content>
              <View style={styles.productHeader}>
                {item.image && (
                  <Image
                    source={{ uri: item.image }}
                    style={styles.productImage}
                    contentFit="cover"
                  />
                )}
                <View style={styles.productInfo}>
                  <Text variant="titleMedium" style={styles.productName}>
                    {item.name}
                  </Text>
                  <Text variant="bodyMedium" style={styles.description}>
                    {item.description}
                  </Text>
                  <Text variant="bodyMedium" style={styles.price}>
                    TSh {item.sellingPrice.toLocaleString()}
                  </Text>
                </View>
              </View>

              <View style={styles.salesInfo}>
                <Text
                  variant="titleMedium"
                  style={[
                    styles.salesText,
                    showLeastSold ? styles.noSalesText : styles.hasSalesText,
                  ]}
                >
                  {showLeastSold
                    ? "No sales in the last month"
                    : `${salesCount} units sold in the last month`}
                </Text>
              </View>
            </Card.Content>
          </Card>
        );
      })}
    </>
  );
}

const styles = StyleSheet.create({
  productCard: {
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
    elevation: 0,
    backgroundColor: "#fff",
  },
  productHeader: {
    flexDirection: "row",
    gap: 12,
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontWeight: "600",
    color: "#333",
  },
  description: {
    color: "#666",
    marginTop: 4,
  },
  price: {
    color: "rgb(31, 114, 161)",
    fontWeight: "600",
    marginTop: 4,
  },
  salesInfo: {
    marginTop: 12,
    alignItems: "flex-end",
  },
  salesText: {
    fontWeight: "600",
  },
  hasSalesText: {
    color: "rgb(69, 196, 99)",
  },
  noSalesText: {
    color: "rgb(187, 70, 82)",
  },
});
