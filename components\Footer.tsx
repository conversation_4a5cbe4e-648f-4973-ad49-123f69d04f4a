import { View, StyleSheet, Text } from "react-native";
import { useTheme } from "react-native-paper";

export function Footer() {
  const theme = useTheme();
  const currentYear = new Date().getFullYear();

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.text}>
          © {currentYear} Talisia{" "}
          <Text style={styles.poweredBy}>Powered by JECS</Text>
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: "#E5E7EB", // Light gray border
    backgroundColor: "#FFFFFF", // White background
  },
  content: {
    alignItems: "center",
  },
  text: {
    color: "#6B7280", // Medium gray text
    fontSize: 14,
    marginBottom: 4,
  },
  poweredBy: {
    color: "#9CA3AF", // Lighter gray text
    fontSize: 12,
    fontStyle: "italic",
  },
});
