import { View, StyleSheet, TouchableOpacity, ScrollView } from "react-native";
import { Text, Card, Button } from "react-native-paper";
import { useRouter } from "expo-router";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useMeContext } from "../../contexts/MeContext";
import {
  Inventory,
  Item,
  Transfer,
  useGetSalesQuery,
  useGetMerchandiseItemsQuery,
  usePaymentsQuery,
  useCreatePaymentMutation,
  PaymentStatus,
  Payment,
} from "../../generated/graphql";
import React from "react";
import { UserGuard } from "../../components/UserGuard";

// Utility functions
const calculateSalesTotals = (sales: any[]) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const currentDay = today.getDay();
  const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay;
  const weekStart = new Date(today);
  weekStart.setDate(today.getDate() + mondayOffset);
  weekStart.setHours(0, 0, 0, 0);

  const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
  monthStart.setHours(0, 0, 0, 0);

  const totals = {
    today: { revenue: 0, profit: 0 },
    thisWeek: { revenue: 0, profit: 0 },
    thisMonth: { revenue: 0, profit: 0 },
    firstSaleTime: "No sales yet",
    topItems: [] as { name: string; quantity: number }[],
  };

  if (!sales?.length) return totals;

  const todayItems: Record<string, number> = {};

  sales.forEach((sale: Inventory) => {
    const saleDate = new Date(Number(sale.createdAt));
    const saleDateStart = new Date(saleDate);
    saleDateStart.setHours(0, 0, 0, 0);

    const revenue = sale.bill?.amount || 0;
    let costOfGoods = 0;

    sale.transfers?.forEach((transfer: Transfer) => {
      const item = sale.items?.find(
        (item: Item) => item.id === transfer.itemId
      );
      if (item) {
        const latestImport = [...(item.imports || [])].sort(
          (a, b) =>
            new Date(b.importDate).getTime() - new Date(a.importDate).getTime()
        )[0];
        const importPrice = latestImport?.importPrice || 0;
        costOfGoods += importPrice * transfer.quantity;

        if (saleDateStart.getTime() === today.getTime()) {
          todayItems[item.name] =
            (todayItems[item.name] || 0) + transfer.quantity;
        }
      }
    });

    const profit = revenue - costOfGoods;

    if (saleDateStart.getTime() === today.getTime()) {
      totals.today.revenue += revenue;
      totals.today.profit += profit;

      if (
        totals.firstSaleTime === "No sales yet" ||
        saleDate < new Date(totals.firstSaleTime)
      ) {
        totals.firstSaleTime = saleDate.toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        });
      }
    }

    if (saleDateStart.getTime() >= weekStart.getTime()) {
      totals.thisWeek.revenue += revenue;
      totals.thisWeek.profit += profit;
    }

    if (saleDateStart.getTime() >= monthStart.getTime()) {
      totals.thisMonth.revenue += revenue;
      totals.thisMonth.profit += profit;
    }
  });

  totals.topItems = Object.entries(todayItems)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 3)
    .map(([name, quantity]) => ({ name, quantity }));

  return totals;
};

export default function DashboardScreen() {
  const router = useRouter();
  const { me } = useMeContext();
  const { data: salesData, loading } = useGetSalesQuery({
    fetchPolicy: "network-only",
    pollInterval: 10000,
  });
  const { data: merchandiseData } = useGetMerchandiseItemsQuery({
    fetchPolicy: "network-only",
  });
  const { data: paymentsData, loading: loadingPayments } = usePaymentsQuery({
    fetchPolicy: "network-only",
  });
  const [addPayment, { loading: addingPayment }] = useCreatePaymentMutation();

  const [activePayment, setActivePayment] = React.useState<Payment | null>(
    null
  );
  const [payments, setPayments] = React.useState<Payment[]>([]);
  const [expiredPayment, setExpiredPayment] = React.useState<Payment | null>(
    null
  );

  React.useEffect(() => {
    if (paymentsData && paymentsData?.payments.length > 0) {
      setPayments(paymentsData.payments as Payment[]);
      const now = new Date();
      const active = paymentsData.payments.find(
        (p) =>
          (p.status === PaymentStatus.Active ||
            p.status === PaymentStatus.Trial) &&
          new Date(isNaN(Number(p.endDate)) ? p.endDate : Number(p.endDate)) >
            now
      );
      const expired = paymentsData.payments.find(
        (p) =>
          (p.status === PaymentStatus.Expired ||
            p.status === PaymentStatus.Trial) &&
          new Date(isNaN(Number(p.endDate)) ? p.endDate : Number(p.endDate)) <
            now
      );
      if (expired) setExpiredPayment(expired as Payment);
      if (active) setActivePayment(active as Payment);
    }
  }, [paymentsData]);

  const handleActivateTrial = async () => {
    try {
      await addPayment({
        variables: {
          input: {
            packageName: "basic",
            billingCycle: "monthly",
            amount: 0,
            autoRenew: false,
            status: "trial",
            endDate: new Date(
              Date.now() + 30 * 24 * 60 * 60 * 1000
            ).toISOString(),
            startDate: new Date().toISOString(),
          },
        },
      });
    } catch (error) {
      console.error("Error activating trial:", error);
    }
  };

  const themeColors = {
    primary: "rgb(31, 114, 161)",
    secondary: "#6c757d",
    success: "#28a745",
    danger: "#dc3545",
    warning: "#ffc107",
    info: "#17a2b8",
    light: "#f8f9fa",
    dark: "#343a40",
    background: "#ffffff",
  };

  const salesTotals = calculateSalesTotals(salesData?.getSales || []);
  const lowStockItems =
    merchandiseData?.getMerchandiseItems
      ?.filter((item) => item.stock <= item.reorder)
      ?.map((item) => ({ name: item.name, quantity: item.stock })) || [];

  const formatCurrency = (amount: number) =>
    `TSh ${amount.toLocaleString("en-TZ", { maximumFractionDigits: 0 })}`;

  const formatDateRange = () => {
    const today = new Date();

    const currentDay = today.getDay();
    const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay;
    const weekStart = new Date(today);
    weekStart.setDate(today.getDate() + mondayOffset);

    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);

    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
    const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    return {
      today: today.toLocaleDateString("en-US", {
        weekday: "long",
        month: "short",
        day: "numeric",
      }),
      week: `Mon ${weekStart.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      })} to Sun ${weekEnd.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      })}`,
      month: `${monthStart.toLocaleDateString("en-US", {
        month: "long",
      })} ${monthStart.getDate()} - ${monthEnd.getDate()}, ${monthStart.getFullYear()}`,
    };
  };

  const dateRanges = formatDateRange();

  const navigateToSales = (tab: "daily" | "weekly" | "monthly") => {
    router.push({
      pathname: "/(tabs)/sales",
      params: { initialTab: tab },
    });
  };

  return (
    <UserGuard>
      <ScrollView style={{ flex: 1, backgroundColor: themeColors.background }}>
        <View style={styles.container}>
          <View style={styles.statsContainer}>
            {!activePayment && !expiredPayment && !loadingPayments && (
              <Card
                style={[styles.card, { borderLeftColor: themeColors.primary }]}
              >
                <Card.Content style={styles.cardContent}>
                  <MaterialCommunityIcons
                    name="package-variant"
                    size={28}
                    color={themeColors.primary}
                  />
                  <View style={styles.cardTextContainer}>
                    <Text style={styles.cardTitle}>No Active Subscription</Text>
                    <Text style={styles.dateText}>
                      Activate a trial to get started
                    </Text>
                    <Button
                      mode="contained"
                      onPress={handleActivateTrial}
                      loading={addingPayment}
                      style={styles.actionButton}
                    >
                      Activate Trial
                    </Button>
                  </View>
                </Card.Content>
              </Card>
            )}

            {expiredPayment && !activePayment && (
              <Card
                style={[styles.card, { borderLeftColor: themeColors.warning }]}
              >
                <Card.Content style={styles.cardContent}>
                  <MaterialCommunityIcons
                    name="package-variant-closed"
                    size={28}
                    color={themeColors.warning}
                  />
                  <View style={styles.cardTextContainer}>
                    <Text style={styles.cardTitle}>Subscription Expired</Text>
                    <Text style={styles.dateText}>
                      Your {payments[0]?.packageName} package has expired on{" "}
                      {new Date(
                        isNaN(Number(expiredPayment.endDate))
                          ? expiredPayment.endDate
                          : Number(expiredPayment.endDate)
                      ).toLocaleDateString()}
                    </Text>
                    <Button
                      mode="contained"
                      onPress={() => router.push("/payment")}
                      style={styles.actionButton}
                    >
                      Renew Subscription
                    </Button>
                  </View>
                </Card.Content>
              </Card>
            )}

            {activePayment && (
              <Card
                style={[styles.card, { borderLeftColor: themeColors.success }]}
              >
                <Card.Content style={styles.cardContent}>
                  <MaterialCommunityIcons
                    name="package-variant-closed"
                    size={28}
                    color={themeColors.success}
                  />
                  <View style={styles.cardTextContainer}>
                    <Text style={styles.cardTitle}>Active Subscription</Text>
                    <Text style={styles.dateText}>
                      {activePayment.packageName} package{" "}
                      <Text
                        style={{
                          color: themeColors.success,
                          fontSize: 16,
                          fontWeight: "bold",
                        }}
                      >
                        {activePayment.status
                          ? activePayment.status === PaymentStatus.Trial
                            ? "trial"
                            : "active"
                          : "active"}
                      </Text>
                    </Text>
                    <Text style={styles.cardValue}>
                      Expires:{" "}
                      {new Date(
                        isNaN(Number(activePayment.endDate))
                          ? activePayment.endDate
                          : Number(activePayment.endDate)
                      ).toLocaleDateString()}
                    </Text>
                    <Button
                      mode="outlined"
                      onPress={() => router.push("/payment")}
                      style={styles.actionButton}
                    >
                      Manage Subscription
                    </Button>
                  </View>
                </Card.Content>
              </Card>
            )}

            <Card style={[styles.card, { borderLeftColor: themeColors.info }]}>
              <Card.Content style={styles.cardContent}>
                <MaterialCommunityIcons
                  name="clock"
                  size={28}
                  color={themeColors.info}
                />
                <View style={styles.cardTextContainer}>
                  <Text style={styles.cardTitle}>Today's Activity</Text>
                  <Text style={styles.dateText}>{dateRanges.today}</Text>
                  <Text style={styles.cardValue}>
                    {loading
                      ? "Loading..."
                      : "First sale at: " + salesTotals.firstSaleTime}
                  </Text>

                  {salesTotals.topItems.length > 0 && (
                    <View style={styles.topItemsContainer}>
                      <Text style={styles.topItemsTitle}>
                        Top Items Sold Today:
                      </Text>
                      {salesTotals.topItems.map((item, index) => (
                        <Text key={index} style={styles.topItem}>
                          {item.name} ({item.quantity})
                        </Text>
                      ))}
                    </View>
                  )}
                </View>
              </Card.Content>
            </Card>

            <TouchableOpacity onPress={() => navigateToSales("daily")}>
              <Card
                style={[styles.card, { borderLeftColor: themeColors.success }]}
              >
                <Card.Content style={styles.cardContent}>
                  <MaterialCommunityIcons
                    name="cash"
                    size={28}
                    color={themeColors.success}
                  />
                  <View style={styles.cardTextContainer}>
                    <Text style={styles.cardTitle}>Today's Sales</Text>
                    <Text style={styles.dateText}>{dateRanges.today}</Text>
                    <Text style={styles.cardValue}>
                      {loading
                        ? "Loading..."
                        : formatCurrency(salesTotals.today.revenue)}
                    </Text>
                    <Text style={styles.profitText}>
                      Profit:{" "}
                      {loading
                        ? "Loading..."
                        : formatCurrency(salesTotals.today.profit)}
                    </Text>
                  </View>
                </Card.Content>
              </Card>
            </TouchableOpacity>

            <TouchableOpacity onPress={() => navigateToSales("weekly")}>
              <Card
                style={[styles.card, { borderLeftColor: themeColors.primary }]}
              >
                <Card.Content style={styles.cardContent}>
                  <MaterialCommunityIcons
                    name="calendar-week"
                    size={28}
                    color={themeColors.primary}
                  />
                  <View style={styles.cardTextContainer}>
                    <Text style={styles.cardTitle}>This Week's Sales</Text>
                    <Text style={styles.dateText}>{dateRanges.week}</Text>
                    <Text style={styles.cardValue}>
                      {loading
                        ? "Loading..."
                        : formatCurrency(salesTotals.thisWeek.revenue)}
                    </Text>
                    <Text style={styles.profitText}>
                      Profit:{" "}
                      {loading
                        ? "Loading..."
                        : formatCurrency(salesTotals.thisWeek.profit)}
                    </Text>
                  </View>
                </Card.Content>
              </Card>
            </TouchableOpacity>

            <TouchableOpacity onPress={() => navigateToSales("monthly")}>
              <Card
                style={[
                  styles.card,
                  { borderLeftColor: themeColors.secondary },
                ]}
              >
                <Card.Content style={styles.cardContent}>
                  <MaterialCommunityIcons
                    name="calendar-month"
                    size={28}
                    color={themeColors.secondary}
                  />
                  <View style={styles.cardTextContainer}>
                    <Text style={styles.cardTitle}>This Month's Sales</Text>
                    <Text style={styles.dateText}>{dateRanges.month}</Text>
                    <Text style={styles.cardValue}>
                      {loading
                        ? "Loading..."
                        : formatCurrency(salesTotals.thisMonth.revenue)}
                    </Text>
                    <Text style={styles.profitText}>
                      Profit:{" "}
                      {loading
                        ? "Loading..."
                        : formatCurrency(salesTotals.thisMonth.profit)}
                    </Text>
                  </View>
                </Card.Content>
              </Card>
            </TouchableOpacity>

            <TouchableOpacity onPress={() => router.push("/(tabs)/inventory")}>
              <Card
                style={[styles.card, { borderLeftColor: themeColors.warning }]}
              >
                <Card.Content style={styles.cardContent}>
                  <MaterialCommunityIcons
                    name="bell-alert"
                    size={28}
                    color={themeColors.warning}
                  />
                  <View style={styles.cardTextContainer}>
                    <Text style={styles.cardTitle}>Low Stock Items</Text>
                    <Text style={styles.cardValue}>
                      {loading ? "Loading..." : `${lowStockItems.length} items`}
                    </Text>
                    {lowStockItems.length > 0 && (
                      <View style={styles.lowStockContainer}>
                        {lowStockItems.slice(0, 3).map((item, index) => (
                          <Text key={index} style={styles.lowStockItem}>
                            {item.name} ({item.quantity})
                          </Text>
                        ))}
                        {lowStockItems.length > 3 && (
                          <Text style={styles.lowStockItem}>
                            +{lowStockItems.length - 3} more...
                          </Text>
                        )}
                      </View>
                    )}
                  </View>
                </Card.Content>
              </Card>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </UserGuard>
  );
}

const styles = StyleSheet.create({
  header: {
    padding: 24,
    paddingTop: 16,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  appbar: {
    backgroundColor: "transparent",
    elevation: 0,
    marginLeft: -8,
    marginTop: -8,
  },
  menuButton: {
    margin: 0,
  },
  companyName: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#ffffff",
    marginBottom: 8,
    marginTop: 8,
    marginLeft: 8,
  },
  welcome: {
    fontSize: 20,
    color: "#f8f9fa",
    fontWeight: "500",
  },
  container: {
    flex: 1,
    padding: 16,
  },
  statsContainer: {
    gap: 16,
  },
  card: {
    width: "100%",
    backgroundColor: "#ffffff",
    borderRadius: 12,
    borderLeftWidth: 4,
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cardContent: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 16,
    padding: 16,
  },
  cardTextContainer: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  cardValue: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 2,
  },
  topItemsContainer: {
    marginTop: 8,
  },
  topItemsTitle: {
    fontSize: 12,
    color: "#6c757d",
    fontWeight: "600",
    marginBottom: 4,
  },
  topItem: {
    fontSize: 12,
    color: "#6c757d",
    marginBottom: 2,
  },
  lowStockContainer: {
    marginTop: 8,
  },
  lowStockItem: {
    fontSize: 12,
    color: "#6c757d",
    marginBottom: 2,
  },
  profitText: {
    fontSize: 15,
    color: "#28a745",
    marginTop: 2,
    fontWeight: "500",
  },
  dateText: {
    fontSize: 14,
    color: "#6c757d",
    marginBottom: 4,
    fontStyle: "italic",
  },
  actionButton: {
    marginTop: 8,
    borderRadius: 8,
  },
});
