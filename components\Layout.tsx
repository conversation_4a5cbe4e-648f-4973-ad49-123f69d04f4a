import { View, StyleSheet } from "react-native";
import { Surface } from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";
import { SideNavigation } from "./SideNavigation";
import { TopNavigation } from "./TopNavigation";
import { Footer } from "./Footer";
import { Drawer } from "react-native-drawer-layout";
import { useDrawer } from "@/contexts/DrawerContext";

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const { isDrawerOpen, setIsDrawerOpen } = useDrawer();

  return (
    <Drawer
      open={isDrawerOpen}
      onOpen={() => setIsDrawerOpen(true)}
      onClose={() => setIsDrawerOpen(false)}
      renderDrawerContent={() => (
        <Surface style={styles.drawer}>
          <SideNavigation />
        </Surface>
      )}
      drawerStyle={styles.drawerContainer}
    >
      <View style={styles.container}>
        <TopNavigation />
        <View style={styles.content}>{children}</View>
        <Footer />
        <SafeAreaView style={styles.bottomSafeArea} edges={["bottom"]} />
      </View>
    </Drawer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F1F5F9",
  },
  content: {
    flex: 1,
  },
  bottomSafeArea: {
    backgroundColor: "rgba(31, 114, 161, 0.8)", // Match footer gradient end color
  },
  drawer: {
    flex: 1,
  },
  drawerContainer: {
    elevation: 20,
    zIndex: 1000,
  },
});
