Talisia Mobile
The mobile application for Talisia. Application for sales management, inventory management, and employee management. 
A mobile application for the web and desktop application to extend data availability beyond the web.
In this application a owner user is able to do the following:
    1. Login to the application
    2. View the inventory for each store and general inventory
    3. View the general sales and store sales
    4. View the employees information and be able to add and edit the information
    5. View the reports (sales, inventory, employees)
    6. View the dashboard which contains sales and inventory information
    7. View the permissions for employees and be able to add and edit the permissions
    8. logout


Application flow:
    0. Splashscreen which contains the logo, when the application is loading check if the user has logged in before, if so go to the dashboard otherwise go to the login screen
    1. Login to the application with email and password, perform frontend validation for email, show a loading screen while the user is logging in. 
    2. If the user is logged in successfully, go to the dashboard otherwise show an error message and go back to the login screen
    3. The authenticated part of the application will have the following structure: side navigation, top navigation, content area and footer. 
    4. The side navigation will contain the routes to the application. 
    5. The top navigation will contain application name and the side navigation trigger button on the far left, the application name will always take the user to the dashboard when clicked.
    6. The content area will contain the content of the application depending on the page the user is on. 
    7. The footer will contain the company name on the left and the logged user name on the right, when the name is clicked the user will be taken to the profile page. 
    8. Dashboard should show the following: Company name, Welcome message with user name, Time for first sale today, sales amount today, sales amount this week and sales amount this month. The tiles showing sales should all be clickable to send user to sales page.
    9. The sales page should show the following:
        i. Sales today and a sales for each store, below it show a graph of sales for every hour since start of sales today. There should be buttons to move between different days that week.
        ii. Sales this week and a sales for each store this week, below it show a graph of sales for every day since start of week. There should be buttons to move between different weeks that month.
        iii. Sales this month and a sales for each store this month. There should be buttons to move between different months that year.
    10. The inventory page should show the products and their quantity in stock. A user should be able to search products by name and sort by quantity. When clicked on a product the user should be able to see the quantities for each store. 
    11. The employees page should show the employees and their information, also a add employee button. A user should be able to search employees by name. When clicked on an employee the user should be taken to the employee profile page.
    12. The employee profile page should show the employee information and here is where the user can edit the employee permissions. 
    13. The add employee page should show a form to add a new employee. 
    14. The side navigation will show the following: Dashboard, Sales, Inventory, Employees, Settings, and Logout which will be on the bottom with a line above it.


GraphQL API endpoints (http://{developing computer ip ************}:4000/graphql on the developing computer where the expo server is running because mobile physical device is in the same network it will be able to connect but for codegen use localhost:4000/graphql as metro server and api in the same computer) as appears in the schemas files in the graphql folder, the generated file from codegen should stay in the location generated/graphql:
    1. Login: Mutation useLoginMutation
    2. Get logged in user: Query useMeQuery
    3. Get all stores: Query useGetStoresQuery
    4. Get all products: Query useGetMerchandiseItemsQuery, and Query useGetItemStoreStocksQuery to get the quantity of the product in each store
    5. Get all employees: Query useGetEmployeesQuery , and useRegisterEmployeeMutation to add a new employee
    6. Get today sales: Query useGetSalesPosQuery
    6. Get all sales this month: Query useGetSalesQuery (use 1st day of month to query sales for that month, if no month is provided then the sales are for that month)
    7. Logout: Mutation useLogoutMutation


Theme:
    1. Primary color:rgb(31, 114, 161)
    2. Secondary color:rgb(21, 33, 43)
    3. Success color:rgb(69, 196, 99)
    4. Danger color:rgb(187, 70, 82)
    5. Warning color: #ffc107
    6. Info color: #17a2b8
    7. Light color: #f8f9fa
    8. Dark color: #343a40
    9. Background color: #ffffff

Tech stack:
    1. React Native
    2. GraphQL
    3. Apollo Client
    4. Expo
    5. TypeScript
    6. React Navigation
    7. React Native Paper
    8. React Native Vector Icons
    9. React Native Reanimated
    10. React Native Gesture Handler
    11. React Native Safe Area Context
    12. React Native Async Storage
    13. graphql codegen


