# Types mutations
mutation deleteType($id: Float!) {
  deleteType(id: $id) {
    ...BooleanResponse
  }
}

mutation addType($args: TypeArgs!) {
  addType(args: $args) {
    status
    error {
      target
      message
    }
    data {
      id
      name
      description
    }
  }
}

mutation editType($id: Float!, $args: TypeEditArgs!) {
  editType(id: $id, args: $args) {
    ...BooleanResponse
  }
}

# Permission mutations
mutation addPermission($name: String!, $userId: Float, $roleId: Float) {
  addPermission(name: $name, userId: $userId, roleId: $roleId) {
    status
    error {
      target
      message
    }
    permission {
      id
      name
    }
  }
}

mutation removePermission($name: String!, $userId: Float, $roleId: Float) {
  removePermission(name: $name, userId: $userId, roleId: $roleId) {
    ...BooleanResponse
  }
}

mutation editPermission($id: Float!, $name: String!) {
  editPermission(id: $id, name: $name) {
    ...BooleanResponse
  }
}

mutation deletePermission($id: Float!) {
  deletePermission(id: $id) {
    ...BooleanResponse
  }
}

# Feature mutations
mutation addFeature($name: String!, $companyId: Float!) {
  addFeature(name: $name, companyId: $companyId) {
    ...BooleanResponse
  }
}

mutation editFeature($id: Float!, $name: String!) {
  editFeature(id: $id, name: $name) {
    ...BooleanResponse
  }
}

mutation deleteFeature($id: Float!) {
  deleteFeature(id: $id) {
    ...BooleanResponse
  }
}

# Roles mutations
mutation addRole($name: String!) {
  addRole(name: $name) {
    ...BooleanResponse
  }
}

mutation editRole($id: Float!, $args: RoleArgs!) {
  editRole(id: $id, args: $args) {
    ...BooleanResponse
  }
}

mutation deleteRole($id: Float!) {
  deleteRole(id: $id) {
    ...BooleanResponse
  }
}

# Category mutations
mutation addCategory($args: CategoryArgs!) {
  addCategory(args: $args) {
    ...BooleanResponse
  }
}

# Adding category with non existing type
mutation addCategoryWithTypeName($args: CategoryTypeArgs!) {
  addCategoryWithTypeName(args: $args) {
    error {
      target
      message
    }
    category {
      id
      name
    }
  }
}

mutation editCategory($id: Float!, $args: CategoryArgs!) {
  editCategory(id: $id, args: $args) {
    ...BooleanResponse
  }
}

mutation editCategoryByName($name: String!, $args: CategoryArgs!) {
  editCategoryByName(name: $name, args: $args) {
    ...BooleanResponse
  }
}

mutation deleteCategory($id: Float!) {
  deleteCategory(id: $id) {
    ...BooleanResponse
  }
}

# expense mutations
mutation addExpense($args: ExpenseInput!) {
  addExpense(args: $args) {
    status
    error {
      target
      message
    }
  }
}

mutation editExpense($id: Float!, $args: ExpenseInput!) {
  editExpense(id: $id, args: $args) {
    status
    error {
      target
      message
    }
  }
}

mutation authorizeExpense($id: Float!) {
  authorizeExpense(id: $id) {
    status
    error {
      target
      message
    }
  }
}

# Note: The deleteExpense mutation is commented out in the resolver,
# but I'll include it here as a soft delete would typically be implemented
mutation deleteExpense($id: Float!) {
  deleteExpense(id: $id) {
    status
    error {
      target
      message
    }
  }
}

# create pament
mutation createPayment($input: CreatePaymentInput!) {
  createPayment(input: $input) {
    id
    status
    packageName
    startDate
    endDate
  }
}

mutation changePaymentStatus($id: Float!, $status: String!) {
  changePaymentStatus(id: $id, status: $status) {
    ...BooleanResponse
  }
}
