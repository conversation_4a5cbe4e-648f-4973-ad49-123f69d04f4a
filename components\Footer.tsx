import { View, StyleSheet, Text } from "react-native";
import { useTheme } from "react-native-paper";
import { LinearGradient } from "expo-linear-gradient";

export function Footer() {
  const theme = useTheme();
  const currentYear = new Date().getFullYear();

  return (
    <LinearGradient
      colors={["rgb(31, 114, 161)", "rgba(31, 114, 161, 0.8)"]}
      style={styles.container}
    >
      <View style={styles.content}>
        <Text style={styles.text}>
          © {currentYear} Talisia{" "}
          <Text style={styles.poweredBy}>Powered by JECS</Text>
        </Text>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 24,
    paddingBottom: 0,
    paddingTop: 4,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    // elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    // zIndex: 1,
  },
  content: {
    alignItems: "center",
  },
  text: {
    color: "#ffffff", // White text to match app bar
    fontSize: 14,
    marginBottom: 4,
    fontWeight: "500",
  },
  poweredBy: {
    color: "#f8f9fa", // Light gray text similar to app bar welcome text
    fontSize: 12,
    fontStyle: "italic",
  },
});
