import { View, StyleSheet } from "react-native";
import { Card, Text } from "react-native-paper";
import { Image } from "expo-image";
import React from "react";
import { Item } from "@/generated/graphql";

export function InventoryList({
  data,
  searchQuery,
  sortByLowStock,
}: {
  data: Item[] | undefined;
  searchQuery: string;
  sortByLowStock: boolean;
}) {
  const filteredAndSortedProducts = React.useMemo(() => {
    let products = data || [];

    // Filter by search query
    if (searchQuery) {
      products = products.filter((product) =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Always sort by low stock
    return [...products].sort((a, b) => {
      const stockA = a.stock;
      const stockB = b.stock;
      return stockA - stockB;
    });
  }, [data, searchQuery]);

  return (
    <>
      {filteredAndSortedProducts.map((product) => (
        <Card key={product.id} style={styles.productCard}>
          <Card.Content style={styles.cardContent}>
            <View style={styles.productHeader}>
              {product.image && (
                <Image
                  source={{ uri: product.image }}
                  style={styles.productImage}
                  contentFit="cover"
                />
              )}
              <View style={styles.productInfo}>
                <Text variant="titleMedium" style={styles.productName}>
                  {product.name}
                </Text>
                <Text variant="bodyMedium" style={styles.description}>
                  {product.description}
                </Text>
                <Text variant="bodyMedium" style={styles.price}>
                  TSh {product.sellingPrice.toLocaleString()}
                </Text>
              </View>
            </View>

            <View style={styles.stockContainer}>
              <View style={styles.stockDetails}>
                <Text variant="bodySmall" style={styles.stockLabel}>
                  Current Stock
                </Text>
                <Text
                  variant="titleMedium"
                  style={[
                    styles.stockText,
                    product.stock === 0
                      ? styles.stockDanger
                      : product.stock <= product.reorder
                      ? styles.stockWarning
                      : styles.stockSuccess,
                  ]}
                >
                  {product.stock} {product.unit}
                </Text>
              </View>

              <View style={styles.stockDetails}>
                <Text variant="bodySmall" style={styles.stockLabel}>
                  Reorder Point
                </Text>
                <Text variant="titleMedium" style={styles.reorderText}>
                  {product.reorder} {product.unit}
                </Text>
              </View>
            </View>

            {product.stock <= product.reorder && (
              <View
                style={[
                  styles.alert,
                  product.stock === 0
                    ? styles.alertDanger
                    : styles.alertWarning,
                ]}
              >
                <Text style={styles.alertText}>
                  {product.stock === 0
                    ? "Out of Stock"
                    : "Stock below reorder point"}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>
      ))}
    </>
  );
}

const styles = StyleSheet.create({
  productCard: {
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
    elevation: 0,
    backgroundColor: "#fff",
    borderWidth: 1,
    borderColor: "#f0f0f0",
  },
  cardContent: {
    padding: 16,
  },
  productHeader: {
    flexDirection: "row",
    gap: 16,
  },
  productImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: "#f5f5f5",
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontWeight: "600",
    color: "#333",
    fontSize: 16,
    marginBottom: 4,
  },
  description: {
    color: "#666",
    fontSize: 14,
    marginBottom: 4,
  },
  price: {
    color: "rgb(31, 114, 161)",
    fontWeight: "600",
    fontSize: 15,
  },
  stockContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
  },
  stockDetails: {
    alignItems: "center",
  },
  stockLabel: {
    color: "#666",
    marginBottom: 4,
    fontSize: 12,
  },
  stockText: {
    fontWeight: "700",
    fontSize: 16,
  },
  reorderText: {
    color: "#666",
    fontWeight: "600",
    fontSize: 16,
  },
  stockDanger: {
    color: "#dc3545",
  },
  stockWarning: {
    color: "#ffc107",
  },
  stockSuccess: {
    color: "#28a745",
  },
  alert: {
    marginTop: 12,
    padding: 8,
    borderRadius: 6,
    alignItems: "center",
  },
  alertWarning: {
    backgroundColor: "rgba(255, 193, 7, 0.1)",
  },
  alertDanger: {
    backgroundColor: "rgba(220, 53, 69, 0.1)",
  },
  alertText: {
    fontSize: 13,
    fontWeight: "600",
    color: "#666",
  },
});
