import { View, StyleSheet } from "react-native";
import { Surface } from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";
import { SideNavigation } from "./SideNavigation";
import { TopNavigation } from "./TopNavigation";
import { Footer } from "./Footer";
import { Drawer } from "react-native-drawer-layout";
import { usePathname } from "expo-router";
import { useDrawer } from "@/contexts/DrawerContext";

interface LayoutProps {
  children: React.ReactNode;
}

export function Layout({ children }: LayoutProps) {
  const pathname = usePathname();
  const isDashboard = pathname === "/(tabs)/dashboard";
  const { isDrawerOpen, setIsDrawerOpen } = useDrawer();

  return (
    <Drawer
      open={isDrawerOpen}
      onOpen={() => setIsDrawerOpen(true)}
      onClose={() => setIsDrawerOpen(false)}
      renderDrawerContent={() => (
        <Surface style={styles.drawer}>
          <SideNavigation />
        </Surface>
      )}
      drawerStyle={styles.drawerContainer}
    >
      <SafeAreaView style={styles.container} edges={["top"]}>
        <TopNavigation showWelcome={isDashboard} />
        <View style={styles.content}>{children}</View>
        <Footer />
        <SafeAreaView style={styles.bottomSafeArea} edges={["bottom"]} />
      </SafeAreaView>
    </Drawer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F1F5F9",
  },
  content: {
    flex: 1,
  },
  bottomSafeArea: {
    backgroundColor: "#FFFFFF", // Match footer background
  },
  drawer: {
    flex: 1,
  },
  drawerContainer: {
    elevation: 20,
    zIndex: 1000,
  },
});
