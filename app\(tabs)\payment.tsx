import React, { useState } from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import {
  Text,
  Card,
  Button,
  Portal,
  Modal,
  Divider,
  ActivityIndicator,
  IconButton,
} from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import {
  usePaymentsQuery,
  useCreatePaymentMutation,
  PaymentStatus,
  Payment,
} from "../../generated/graphql";
import { formatDate } from "@/constants/functions";

export default function PaymentScreen() {
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState("basic");

  const {
    data: paymentsData,
    loading: loadingPayments,
    refetch,
  } = usePaymentsQuery({
    fetchPolicy: "network-only",
  });

  const [createPayment, { loading: creatingPayment }] =
    useCreatePaymentMutation();

  const [activePayment, setActivePayment] = React.useState<Payment | null>(
    null
  );
  const [payments, setPayments] = React.useState<Payment[]>([]);
  const [pendingPayment, setPendingPayment] = React.useState<Payment | null>(
    null
  );

  React.useEffect(() => {
    if (paymentsData && paymentsData?.payments.length > 0) {
      setPayments(paymentsData.payments as Payment[]);
      const now = new Date();
      const active = paymentsData.payments.find(
        (p) =>
          (p.status === PaymentStatus.Active ||
            p.status === PaymentStatus.Trial) &&
          new Date(isNaN(Number(p.endDate)) ? p.endDate : Number(p.endDate)) >
            now
      );
      const pending = paymentsData.payments.find(
        (p) =>
          p.status === PaymentStatus.Pending &&
          new Date(isNaN(Number(p.endDate)) ? p.endDate : Number(p.endDate)) >
            now
      );
      if (active) setActivePayment(active as Payment);
      if (pending) setPendingPayment(pending as Payment);
    }
  }, [paymentsData]);

  const handleSubscribe = (packageName: string) => {
    setSelectedPackage(packageName);
    setPaymentModalVisible(true);
  };

  const handleConfirmPayment = async () => {
    try {
      // Calculate start date (end of current subscription or now)
      const startDate = activePayment
        ? new Date(activePayment.endDate)
        : new Date();

      // Calculate end date (1 month after start date)
      const endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + 1);

      await createPayment({
        variables: {
          input: {
            packageName: selectedPackage,
            billingCycle: "monthly",
            amount: 100000,
            autoRenew: false,
            status: "pending",
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString(),
          },
        },
      });

      setPaymentModalVisible(false);
      refetch();
    } catch (error) {
      console.error("Error creating payment:", error);
    }
  };

  const getStatusColor = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.Active:
        return "#28a745";
      case PaymentStatus.Pending:
        return "#ffc107";
      case PaymentStatus.Trial:
        return "#17a2b8";
      case PaymentStatus.Expired:
        return "#dc3545";
      default:
        return "#6c757d";
    }
  };

  const getStatusIcon = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.Active:
        return "check-circle";
      case PaymentStatus.Pending:
        return "clock-outline";
      case PaymentStatus.Trial:
        return "gift";
      case PaymentStatus.Expired:
        return "close-circle";
      default:
        return "help-circle";
    }
  };

  if (loadingPayments) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="rgb(31, 114, 161)" />
        <Text style={styles.loadingText}>Loading subscription data...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text variant="headlineMedium" style={styles.title}>
          Subscription
        </Text>
      </View>

      {/* Current Subscription */}
      <Card style={styles.currentSubscriptionCard}>
        <Card.Content>
          <Text variant="titleLarge" style={styles.sectionTitle}>
            Current Subscription
          </Text>

          {activePayment ? (
            <View style={styles.activeSubscription}>
              <View style={styles.subscriptionHeader}>
                <View style={styles.packageBadge}>
                  <Text style={styles.packageName}>
                    {activePayment.packageName}
                  </Text>
                </View>
                <View
                  style={[
                    styles.statusBadge,
                    { backgroundColor: getStatusColor(activePayment.status) },
                  ]}
                >
                  <MaterialCommunityIcons
                    name={getStatusIcon(activePayment.status)}
                    size={14}
                    color="white"
                  />
                  <Text style={styles.statusText}>{activePayment.status}</Text>
                </View>
              </View>

              <Divider style={styles.divider} />

              <View style={styles.subscriptionDetails}>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Start Date:</Text>
                  <Text style={styles.detailValue}>
                    {formatDate(activePayment.startDate)}
                  </Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>End Date:</Text>
                  <Text style={styles.detailValue}>
                    {formatDate(activePayment.endDate)}
                  </Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Billing Cycle:</Text>
                  <Text style={styles.detailValue}>
                    {activePayment.billingCycle}
                  </Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Amount:</Text>
                  <Text style={styles.detailValue}>
                    TSh {activePayment.amount.toLocaleString()}
                  </Text>
                </View>
              </View>
            </View>
          ) : (
            <View style={styles.noSubscription}>
              <MaterialCommunityIcons
                name="package-variant-closed"
                size={48}
                color="#6c757d"
              />
              <Text style={styles.noSubscriptionText}>
                No active subscription found
              </Text>
              <Text style={styles.noSubscriptionSubtext}>
                Choose a package below to subscribe
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>

      {/* Pending Payment */}
      {pendingPayment && (
        <Card style={styles.pendingCard}>
          <Card.Content>
            <View style={styles.pendingHeader}>
              <MaterialCommunityIcons
                name="clock-outline"
                size={24}
                color="#ffc107"
              />
              <Text style={styles.pendingTitle}>Pending Payment</Text>
            </View>
            <Text style={styles.pendingText}>
              Your {pendingPayment.packageName} subscription payment is being
              processed.
            </Text>
            <Text style={styles.pendingSubtext}>
              This may take up to 24 hours to verify.
            </Text>
          </Card.Content>
        </Card>
      )}

      {/* Available Packages */}
      <Text variant="titleLarge" style={styles.packagesTitle}>
        Available Packages
      </Text>

      <Card style={styles.packageCard}>
        <Card.Content>
          <View style={styles.packageHeader}>
            <Text style={styles.packageTitle}>Basic</Text>
            <Text style={styles.packagePrice}>TSh 100,000</Text>
          </View>
          <Text style={styles.packagePeriod}>per month</Text>

          <Divider style={styles.divider} />

          <View style={styles.featureList}>
            <View style={styles.feature}>
              <MaterialCommunityIcons
                name="check-circle"
                size={20}
                color="rgb(31, 114, 161)"
              />
              <Text style={styles.featureText}>Unlimited sales</Text>
            </View>
            <View style={styles.feature}>
              <MaterialCommunityIcons
                name="check-circle"
                size={20}
                color="rgb(31, 114, 161)"
              />
              <Text style={styles.featureText}>Inventory management</Text>
            </View>
            <View style={styles.feature}>
              <MaterialCommunityIcons
                name="check-circle"
                size={20}
                color="rgb(31, 114, 161)"
              />
              <Text style={styles.featureText}>Basic reporting</Text>
            </View>
            <View style={styles.feature}>
              <MaterialCommunityIcons
                name="check-circle"
                size={20}
                color="rgb(31, 114, 161)"
              />
              <Text style={styles.featureText}>Up to 5 users</Text>
            </View>
          </View>

          <Button
            mode="contained"
            onPress={() => handleSubscribe("basic")}
            style={styles.subscribeButton}
            disabled={!!pendingPayment || creatingPayment}
          >
            {creatingPayment ? "Processing..." : "Subscribe"}
          </Button>
        </Card.Content>
      </Card>

      {/* Payment Modal */}
      <Portal>
        <Modal
          visible={paymentModalVisible}
          onDismiss={() => setPaymentModalVisible(false)}
          contentContainerStyle={styles.modalContainer}
        >
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Payment Information</Text>
            <IconButton
              icon="close"
              size={20}
              onPress={() => setPaymentModalVisible(false)}
            />
          </View>

          <Divider style={styles.divider} />

          <View style={styles.paymentDetails}>
            <Text style={styles.paymentInstructions}>
              Please send payment to the following MPESA number:
            </Text>

            <View style={styles.paymentInfo}>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Name:</Text>
                <Text style={styles.paymentValue}>Samwel Ngwale</Text>
              </View>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Phone:</Text>
                <Text style={styles.paymentValue}>0744-648-170</Text>
              </View>
              <View style={styles.paymentRow}>
                <Text style={styles.paymentLabel}>Amount:</Text>
                <Text style={styles.paymentValue}>TSh 100,000</Text>
              </View>
            </View>

            <Text style={styles.paymentNote}>
              After sending payment, click "PAID" to complete your subscription.
            </Text>
          </View>

          <View style={styles.modalActions}>
            <Button
              mode="outlined"
              onPress={() => setPaymentModalVisible(false)}
              style={styles.cancelButton}
            >
              Later
            </Button>
            <Button
              mode="contained"
              onPress={handleConfirmPayment}
              style={styles.paidButton}
              loading={creatingPayment}
              disabled={creatingPayment}
            >
              PAID
            </Button>
          </View>
        </Modal>
      </Portal>

      {/* Payment History */}
      <Text variant="titleLarge" style={styles.historyTitle}>
        Payment History
      </Text>

      {payments.length > 0 ? (
        payments.map((payment) => (
          <Card key={payment.id} style={styles.historyCard}>
            <Card.Content>
              <View style={styles.historyHeader}>
                <View>
                  <Text style={styles.historyPackage}>
                    {payment.packageName}
                  </Text>
                  <Text style={styles.historyDate}>
                    {payment.status === PaymentStatus.Pending && activePayment
                      ? (() => {
                          const startDate = new Date(
                            Number(activePayment.endDate)
                          );
                          const endDate = new Date(
                            Number(activePayment.endDate)
                          );
                          endDate.setMonth(endDate.getMonth() + 1);
                          return `${formatDate(
                            startDate.toISOString()
                          )} - ${formatDate(endDate.toISOString())}`;
                        })()
                      : `${formatDate(payment.startDate)} - ${formatDate(
                          payment.endDate
                        )}`}
                  </Text>
                </View>
                <View
                  style={[
                    styles.historyStatus,
                    { backgroundColor: getStatusColor(payment.status) },
                  ]}
                >
                  <Text style={styles.historyStatusText}>{payment.status}</Text>
                </View>
              </View>
              <View style={styles.historyAmount}>
                <Text style={styles.historyAmountText}>
                  TSh {payment.amount.toLocaleString()}
                </Text>
              </View>
            </Card.Content>
          </Card>
        ))
      ) : (
        <Card style={styles.emptyHistoryCard}>
          <Card.Content>
            <Text style={styles.emptyHistoryText}>
              No payment history found
            </Text>
          </Card.Content>
        </Card>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#666",
  },
  header: {
    padding: 16,
  },
  title: {
    color: "rgb(31, 114, 161)",
    fontWeight: "bold",
  },
  currentSubscriptionCard: {
    margin: 16,
    borderRadius: 12,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 16,
    color: "#333",
  },
  activeSubscription: {
    marginTop: 8,
  },
  subscriptionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  packageBadge: {
    backgroundColor: "rgb(31, 114, 161)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  packageName: {
    color: "white",
    fontWeight: "bold",
    textTransform: "uppercase",
  },
  statusBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 16,
  },
  statusText: {
    color: "white",
    fontWeight: "500",
    fontSize: 12,
    marginLeft: 4,
  },
  divider: {
    marginVertical: 12,
  },
  subscriptionDetails: {
    marginTop: 8,
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  detailLabel: {
    color: "#666",
    fontSize: 14,
  },
  detailValue: {
    fontWeight: "500",
    fontSize: 14,
  },
  noSubscription: {
    alignItems: "center",
    padding: 20,
  },
  noSubscriptionText: {
    fontSize: 18,
    fontWeight: "500",
    marginTop: 12,
    color: "#333",
  },
  noSubscriptionSubtext: {
    fontSize: 14,
    color: "#666",
    marginTop: 8,
    textAlign: "center",
  },
  pendingCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: "#fff9e6",
    borderLeftWidth: 4,
    borderLeftColor: "#ffc107",
  },
  pendingHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  pendingTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginLeft: 8,
    color: "#333",
  },
  pendingText: {
    fontSize: 14,
    color: "#333",
    marginBottom: 4,
  },
  pendingSubtext: {
    fontSize: 12,
    color: "#666",
  },
  packagesTitle: {
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    color: "#333",
  },
  packageCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  packageHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "baseline",
  },
  packageTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
  },
  packagePrice: {
    fontSize: 24,
    fontWeight: "bold",
    color: "rgb(31, 114, 161)",
  },
  packagePeriod: {
    fontSize: 14,
    color: "#666",
    textAlign: "right",
  },
  featureList: {
    marginTop: 12,
    marginBottom: 16,
  },
  feature: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  featureText: {
    marginLeft: 8,
    fontSize: 14,
    color: "#333",
  },
  subscribeButton: {
    borderRadius: 8,
    backgroundColor: "rgb(31, 114, 161)",
  },
  historyTitle: {
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 8,
    color: "#333",
  },
  historyCard: {
    marginHorizontal: 16,
    marginBottom: 12,
    borderRadius: 12,
    elevation: 1,
  },
  historyHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  historyPackage: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
  },
  historyDate: {
    fontSize: 12,
    color: "#666",
    marginTop: 2,
  },
  historyStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  historyStatusText: {
    color: "white",
    fontSize: 12,
    fontWeight: "500",
  },
  historyAmount: {
    marginTop: 8,
  },
  historyAmountText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  emptyHistoryCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    elevation: 1,
  },
  emptyHistoryText: {
    textAlign: "center",
    padding: 16,
    color: "#666",
  },
  modalContainer: {
    backgroundColor: "white",
    margin: 20,
    borderRadius: 12,
    padding: 0,
    overflow: "hidden",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#333",
  },
  paymentDetails: {
    padding: 16,
  },
  paymentInstructions: {
    fontSize: 16,
    color: "#333",
    marginBottom: 16,
  },
  paymentInfo: {
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  paymentRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  paymentLabel: {
    fontSize: 14,
    color: "#666",
  },
  paymentValue: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
  },
  paymentNote: {
    fontSize: 14,
    color: "#666",
    fontStyle: "italic",
  },
  modalActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#e5e7eb",
  },
  cancelButton: {
    marginRight: 12,
    borderColor: "#6c757d",
  },
  paidButton: {
    backgroundColor: "rgb(31, 114, 161)",
  },
});
