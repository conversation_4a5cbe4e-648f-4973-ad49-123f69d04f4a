query getCompany($id: Float!) {
  getCompany(id: $id) {
    id
    name
    tinNumber
    registrationNumber
    type
    employees {
      id
      role {
        name
      }
    }
    features {
      id
      name
    }
    payments {
      id
      status
      startDate
      endDate
      packageName
      billingCycle
      amount
    }
    location
  }
}

query getCompanies {
  getCompanies {
    id
    name
    tinNumber
    registrationNumber
    type
    employees {
      id
      role {
        name
      }
    }
    features {
      id
      name
    }
    payments {
      id
      status
      startDate
      endDate
      packageName
      billingCycle
      amount
    }
    location
  }
}

query getSchedules($ownerId: Int!, $owner: String!) {
  getSchedules(ownerId: $ownerId, owner: $owner) {
    id
    onTime
    offTime
    day
    description
    clinicId
    employeeId
  }
}
