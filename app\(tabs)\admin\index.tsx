import React from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Animated,
} from "react-native";
import {
  Text,
  Card,
  ActivityIndicator,
  Chip,
  Avatar,
  Surface,
} from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import {
  useGetCompaniesQuery,
  PaymentStatus,
  BillingCycle,
} from "../../../generated/graphql";

const { width } = Dimensions.get("window");

export default function AdminScreen() {
  const router = useRouter();
  const { data, loading, error, refetch } = useGetCompaniesQuery({
    fetchPolicy: "network-only",
    pollInterval: 30000, // Refresh every 30 seconds
  });

  const companies = data?.getCompanies || [];

  const getPaymentStatusColor = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.Active:
        return "#10B981"; // Green
      case PaymentStatus.Pending:
        return "#F59E0B"; // Amber
      case PaymentStatus.Expired:
        return "#EF4444"; // Red
      case PaymentStatus.Trial:
        return "#8B5CF6"; // Purple
      default:
        return "#6B7280"; // Gray
    }
  };

  const getPaymentStatusIcon = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.Active:
        return "check-circle";
      case PaymentStatus.Pending:
        return "clock-outline";
      case PaymentStatus.Expired:
        return "alert-circle";
      case PaymentStatus.Trial:
        return "star-outline";
      default:
        return "help-circle";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getLatestPayment = (payments: any[]) => {
    if (!payments || payments.length === 0) return null;
    return payments.sort(
      (a, b) =>
        new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
    )[0];
  };

  const handleCompanyPress = (companyId: number) => {
    router.push(`/(tabs)/admin/company/${companyId}`);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1F72A1" />
        <Text style={styles.loadingText}>Loading companies...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="alert-circle" size={48} color="#EF4444" />
        <Text style={styles.errorText}>Failed to load companies</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#1F72A1", "#3B82F6"]}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Text style={styles.headerTitle}>Admin Dashboard</Text>
        <Text style={styles.headerSubtitle}>
          {companies.length} Companies Registered
        </Text>
      </LinearGradient>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {companies.map((company) => {
          const latestPayment = getLatestPayment(company.payments || []);
          const employeeCount = company.employees?.length || 0;
          const featureCount = company.features?.length || 0;

          return (
            <TouchableOpacity
              key={company.id}
              onPress={() => handleCompanyPress(company.id)}
              activeOpacity={0.7}
            >
              <Surface style={styles.companyCard} elevation={4}>
                <LinearGradient
                  colors={["#FFFFFF", "#F8FAFC"]}
                  style={styles.cardGradient}
                >
                  <View style={styles.cardHeader}>
                    <View style={styles.companyInfo}>
                      <Avatar.Text
                        size={48}
                        label={company.name.substring(0, 2).toUpperCase()}
                        style={styles.companyAvatar}
                        labelStyle={styles.avatarLabel}
                      />
                      <View style={styles.companyDetails}>
                        <Text style={styles.companyName}>{company.name}</Text>
                        <Text style={styles.companyLocation}>
                          {company.location}
                        </Text>
                        <Text style={styles.companyType}>{company.type}</Text>
                      </View>
                    </View>
                    {latestPayment && (
                      <Chip
                        icon={() => (
                          <MaterialCommunityIcons
                            name={getPaymentStatusIcon(latestPayment.status)}
                            size={16}
                            color="#FFFFFF"
                          />
                        )}
                        style={[
                          styles.statusChip,
                          {
                            backgroundColor: getPaymentStatusColor(
                              latestPayment.status
                            ),
                          },
                        ]}
                        textStyle={styles.statusChipText}
                      >
                        {latestPayment.status}
                      </Chip>
                    )}
                  </View>

                  <View style={styles.cardStats}>
                    <View style={styles.statItem}>
                      <MaterialCommunityIcons
                        name="account-group"
                        size={20}
                        color="#6B7280"
                      />
                      <Text style={styles.statValue}>{employeeCount}</Text>
                      <Text style={styles.statLabel}>Employees</Text>
                    </View>
                    <View style={styles.statItem}>
                      <MaterialCommunityIcons
                        name="feature-search"
                        size={20}
                        color="#6B7280"
                      />
                      <Text style={styles.statValue}>{featureCount}</Text>
                      <Text style={styles.statLabel}>Features</Text>
                    </View>
                    {latestPayment && (
                      <View style={styles.statItem}>
                        <MaterialCommunityIcons
                          name="currency-usd"
                          size={20}
                          color="#6B7280"
                        />
                        <Text style={styles.statValue}>
                          {formatCurrency(latestPayment.amount)}
                        </Text>
                        <Text style={styles.statLabel}>
                          {latestPayment.billingCycle}
                        </Text>
                      </View>
                    )}
                  </View>

                  {latestPayment && (
                    <View style={styles.paymentInfo}>
                      <View style={styles.paymentRow}>
                        <Text style={styles.paymentLabel}>Package:</Text>
                        <Text style={styles.paymentValue}>
                          {latestPayment.packageName}
                        </Text>
                      </View>
                      <View style={styles.paymentRow}>
                        <Text style={styles.paymentLabel}>Period:</Text>
                        <Text style={styles.paymentValue}>
                          {formatDate(latestPayment.startDate)} -{" "}
                          {formatDate(latestPayment.endDate)}
                        </Text>
                      </View>
                    </View>
                  )}

                  <View style={styles.cardFooter}>
                    <View style={styles.companyMeta}>
                      <Text style={styles.metaText}>
                        TIN: {company.tinNumber}
                      </Text>
                      <Text style={styles.metaText}>
                        Reg: {company.registrationNumber}
                      </Text>
                    </View>
                    <MaterialCommunityIcons
                      name="chevron-right"
                      size={24}
                      color="#1F72A1"
                    />
                  </View>
                </LinearGradient>
              </Surface>
            </TouchableOpacity>
          );
        })}

        {companies.length === 0 && (
          <View style={styles.emptyState}>
            <MaterialCommunityIcons
              name="office-building"
              size={64}
              color="#9CA3AF"
            />
            <Text style={styles.emptyStateTitle}>No Companies Found</Text>
            <Text style={styles.emptyStateText}>
              No companies are currently registered in the system.
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F1F5F9",
  },
  header: {
    paddingTop: 60,
    paddingBottom: 24,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: "#E2E8F0",
    opacity: 0.9,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  companyCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: "#1F72A1",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  cardGradient: {
    padding: 20,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 16,
  },
  companyInfo: {
    flexDirection: "row",
    flex: 1,
    marginRight: 12,
  },
  companyAvatar: {
    backgroundColor: "#1F72A1",
    marginRight: 12,
  },
  avatarLabel: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  companyDetails: {
    flex: 1,
  },
  companyName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#1E293B",
    marginBottom: 2,
  },
  companyLocation: {
    fontSize: 14,
    color: "#64748B",
    marginBottom: 2,
  },
  companyType: {
    fontSize: 12,
    color: "#94A3B8",
    textTransform: "capitalize",
  },
  statusChip: {
    borderRadius: 20,
  },
  statusChipText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
  cardStats: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 16,
    paddingVertical: 12,
    backgroundColor: "#F8FAFC",
    borderRadius: 12,
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statValue: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#1E293B",
    marginTop: 4,
  },
  statLabel: {
    fontSize: 12,
    color: "#64748B",
    marginTop: 2,
  },
  paymentInfo: {
    backgroundColor: "#F1F5F9",
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
  },
  paymentRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  paymentLabel: {
    fontSize: 14,
    color: "#64748B",
    fontWeight: "500",
  },
  paymentValue: {
    fontSize: 14,
    color: "#1E293B",
    fontWeight: "600",
    flex: 1,
    textAlign: "right",
  },
  cardFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "#E2E8F0",
  },
  companyMeta: {
    flex: 1,
  },
  metaText: {
    fontSize: 12,
    color: "#94A3B8",
    marginBottom: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F1F5F9",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#64748B",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F1F5F9",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: "#EF4444",
    marginTop: 16,
    marginBottom: 24,
    textAlign: "center",
  },
  retryButton: {
    backgroundColor: "#1F72A1",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 60,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#64748B",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: "#94A3B8",
    textAlign: "center",
    lineHeight: 24,
  },
});
