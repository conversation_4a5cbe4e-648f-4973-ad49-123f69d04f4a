import React from "react";
import { View, StyleSheet, ScrollView, TouchableOpacity } from "react-native";
import { Text, ActivityIndicator, Avatar, Surface } from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import {
  useGetCompaniesQuery,
  PaymentStatus,
} from "../../../generated/graphql";
import { formatCurrency, formatDate } from "@/constants/functions";

export default function AdminScreen() {
  const router = useRouter();
  const { data, loading, error, refetch } = useGetCompaniesQuery({
    fetchPolicy: "network-only",
    pollInterval: 30000, // Refresh every 30 seconds
  });

  const companies = data?.getCompanies || [];

  const getPaymentStatusColor = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.Active:
        return "#10B981"; // Green
      case PaymentStatus.Pending:
        return "#F59E0B"; // Amber
      case PaymentStatus.Expired:
        return "#EF4444"; // Red
      case PaymentStatus.Trial:
        return "#8B5CF6"; // Purple
      default:
        return "#6B7280"; // Gray
    }
  };

  const getPaymentStatusIcon = (status: PaymentStatus) => {
    switch (status) {
      case PaymentStatus.Active:
        return "check-circle";
      case PaymentStatus.Pending:
        return "clock-outline";
      case PaymentStatus.Expired:
        return "alert-circle";
      case PaymentStatus.Trial:
        return "star-outline";
      default:
        return "help-circle";
    }
  };

  // Get active payment (either Active or Trial with future end date)
  const getActivePayment = (payments: any[]) => {
    if (!payments || payments.length === 0) return null;

    const now = new Date();

    // First check for active payments with future end date
    const activePayment = payments.find(
      (p) =>
        (p.status === PaymentStatus.Active ||
          p.status === PaymentStatus.Trial) &&
        new Date(isNaN(Number(p.endDate)) ? p.endDate : Number(p.endDate)) > now
    );

    if (activePayment) return activePayment;

    // If no active payment, check for pending payments
    const pendingPayment = payments.find(
      (p) => p.status === PaymentStatus.Pending
    );
    if (pendingPayment) return pendingPayment;

    // If no active or pending payment, return the most recent payment
    return payments.sort(
      (a, b) =>
        new Date(b.startDate).getTime() - new Date(a.startDate).getTime()
    )[0];
  };

  const handleCompanyPress = (companyId: number) => {
    router.push(`/(tabs)/admin/company/${companyId}`);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1F72A1" />
        <Text style={styles.loadingText}>Loading companies...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="alert-circle" size={48} color="#EF4444" />
        <Text style={styles.errorText}>Failed to load companies</Text>
        <TouchableOpacity style={styles.retryButton} onPress={() => refetch()}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#1F72A1", "#3B82F6"]}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Text style={styles.headerTitle}>Admin Dashboard</Text>
        <View style={styles.statsContainer}>
          <View style={styles.statBadge}>
            <MaterialCommunityIcons
              name="office-building"
              size={20}
              color="#FFFFFF"
            />
            <Text style={styles.statText}>{companies.length} Companies</Text>
          </View>
          <View style={styles.statBadge}>
            <MaterialCommunityIcons
              name="account-group"
              size={20}
              color="#FFFFFF"
            />
            <Text style={styles.statText}>
              {companies.reduce(
                (total, company) => total + (company.employees?.length || 0),
                0
              )}{" "}
              Users
            </Text>
          </View>
        </View>
      </LinearGradient>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {companies.map((company) => {
          const activePayment = getActivePayment(company.payments || []);
          const employeeCount = company.employees?.length || 0;
          const featureCount = company.features?.length || 0;

          // Determine if subscription is active
          const now = new Date();
          const isActive =
            activePayment &&
            (activePayment.status === PaymentStatus.Active ||
              activePayment.status === PaymentStatus.Trial) &&
            new Date(
              isNaN(Number(activePayment.endDate))
                ? activePayment.endDate
                : Number(activePayment.endDate)
            ) > now;

          // Get status color for the card border
          const statusColor = activePayment
            ? getPaymentStatusColor(activePayment.status)
            : "#6B7280";

          // Get payment status for display
          const paymentStatus = activePayment
            ? activePayment.status
            : "No Subscription";

          return (
            <TouchableOpacity
              key={company.id}
              onPress={() => handleCompanyPress(company.id)}
              activeOpacity={0.7}
            >
              <Surface
                style={[
                  styles.companyCard,
                  { borderLeftWidth: 4, borderLeftColor: statusColor },
                ]}
                elevation={4}
              >
                <LinearGradient
                  colors={
                    isActive ? ["#111827", "#1E3A8A"] : ["#1F2937", "#374151"]
                  }
                  style={styles.cardGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  {/* Large Payment Status Badge at the top */}
                  <View style={styles.paymentStatusBanner}>
                    <MaterialCommunityIcons
                      name={getPaymentStatusIcon(
                        activePayment?.status || PaymentStatus.Expired
                      )}
                      size={20}
                      color="#FFFFFF"
                    />
                    <Text style={styles.paymentStatusText}>
                      {paymentStatus}
                    </Text>
                  </View>

                  <View style={styles.cardHeader}>
                    <View style={styles.companyInfo}>
                      <Avatar.Text
                        size={48}
                        label={company.name.substring(0, 2).toUpperCase()}
                        style={[
                          styles.companyAvatar,
                          { backgroundColor: isActive ? "#3B82F6" : "#6B7280" },
                        ]}
                        labelStyle={styles.avatarLabel}
                      />
                      <View style={styles.companyDetails}>
                        <Text style={styles.companyName}>{company.name}</Text>
                        <Text style={styles.companyLocation}>
                          {company.location}
                        </Text>
                        <Text style={styles.companyType}>{company.type}</Text>
                      </View>
                    </View>
                  </View>

                  <View style={styles.cardStats}>
                    <View style={styles.statItem}>
                      <MaterialCommunityIcons
                        name="account-group"
                        size={20}
                        color={isActive ? "#60A5FA" : "#9CA3AF"}
                      />
                      <Text
                        style={[
                          styles.statValue,
                          { color: isActive ? "#FFFFFF" : "#D1D5DB" },
                        ]}
                      >
                        {employeeCount}
                      </Text>
                      <Text
                        style={[
                          styles.statLabel,
                          { color: isActive ? "#A5B4FC" : "#9CA3AF" },
                        ]}
                      >
                        Employees
                      </Text>
                    </View>
                    <View style={styles.statItem}>
                      <MaterialCommunityIcons
                        name="feature-search"
                        size={20}
                        color={isActive ? "#60A5FA" : "#9CA3AF"}
                      />
                      <Text
                        style={[
                          styles.statValue,
                          { color: isActive ? "#FFFFFF" : "#D1D5DB" },
                        ]}
                      >
                        {featureCount}
                      </Text>
                      <Text
                        style={[
                          styles.statLabel,
                          { color: isActive ? "#A5B4FC" : "#9CA3AF" },
                        ]}
                      >
                        Features
                      </Text>
                    </View>
                    {activePayment && (
                      <View style={styles.statItem}>
                        <MaterialCommunityIcons
                          name="currency-usd"
                          size={20}
                          color={isActive ? "#60A5FA" : "#9CA3AF"}
                        />
                        <Text
                          style={[
                            styles.statValue,
                            { color: isActive ? "#FFFFFF" : "#D1D5DB" },
                          ]}
                        >
                          {formatCurrency(activePayment.amount)}
                        </Text>
                        <Text
                          style={[
                            styles.statLabel,
                            { color: isActive ? "#A5B4FC" : "#9CA3AF" },
                          ]}
                        >
                          {activePayment.billingCycle}
                        </Text>
                      </View>
                    )}
                  </View>

                  {activePayment && (
                    <View
                      style={[
                        styles.paymentInfo,
                        {
                          backgroundColor: isActive
                            ? "rgba(59, 130, 246, 0.1)"
                            : "rgba(75, 85, 99, 0.1)",
                        },
                      ]}
                    >
                      <View style={styles.paymentRow}>
                        <Text
                          style={[
                            styles.paymentLabel,
                            { color: isActive ? "#A5B4FC" : "#9CA3AF" },
                          ]}
                        >
                          Package:
                        </Text>
                        <Text
                          style={[
                            styles.paymentValue,
                            { color: isActive ? "#FFFFFF" : "#D1D5DB" },
                          ]}
                        >
                          {activePayment.packageName}
                        </Text>
                      </View>
                      <View style={styles.paymentRow}>
                        <Text
                          style={[
                            styles.paymentLabel,
                            { color: isActive ? "#A5B4FC" : "#9CA3AF" },
                          ]}
                        >
                          Period:
                        </Text>
                        <Text
                          style={[
                            styles.paymentValue,
                            { color: isActive ? "#FFFFFF" : "#D1D5DB" },
                          ]}
                        >
                          {formatDate(`${Number(activePayment.startDate)}`)} -{" "}
                          {formatDate(
                            isNaN(Number(activePayment.endDate))
                              ? activePayment.endDate
                              : Number(activePayment.endDate)
                          )}
                        </Text>
                      </View>
                    </View>
                  )}

                  <View
                    style={[
                      styles.cardFooter,
                      {
                        borderTopColor: isActive
                          ? "rgba(59, 130, 246, 0.2)"
                          : "rgba(75, 85, 99, 0.2)",
                      },
                    ]}
                  >
                    <View style={styles.companyMeta}>
                      <Text
                        style={[
                          styles.metaText,
                          { color: isActive ? "#A5B4FC" : "#9CA3AF" },
                        ]}
                      >
                        TIN: {company.tinNumber}
                      </Text>
                      <Text
                        style={[
                          styles.metaText,
                          { color: isActive ? "#A5B4FC" : "#9CA3AF" },
                        ]}
                      >
                        Reg: {company.registrationNumber}
                      </Text>
                    </View>
                    <MaterialCommunityIcons
                      name="chevron-right"
                      size={24}
                      color={isActive ? "#60A5FA" : "#9CA3AF"}
                    />
                  </View>
                </LinearGradient>
              </Surface>
            </TouchableOpacity>
          );
        })}

        {companies.length === 0 && (
          <View style={styles.emptyState}>
            <MaterialCommunityIcons
              name="office-building"
              size={64}
              color="#9CA3AF"
            />
            <Text style={styles.emptyStateTitle}>No Companies Found</Text>
            <Text style={styles.emptyStateText}>
              No companies are currently registered in the system.
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#0F172A", // Dark blue background for futuristic feel
  },
  header: {
    paddingTop: 10,
    paddingBottom: 10,
    paddingHorizontal: 20,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  statBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 6,
  },
  statText: {
    color: "#FFFFFF",
    fontWeight: "600",
    fontSize: 14,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  companyCard: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: "hidden",
    shadowColor: "#1F72A1",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 10,
  },
  cardGradient: {
    padding: 0, // Remove padding to allow banner to stretch full width
  },
  paymentStatusBanner: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    paddingVertical: 8,
    gap: 8,
  },
  paymentStatusText: {
    color: "#FFFFFF",
    fontWeight: "bold",
    fontSize: 16,
    textTransform: "uppercase",
    letterSpacing: 1,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginTop: 16,
    marginHorizontal: 20,
    marginBottom: 16,
  },
  companyInfo: {
    flexDirection: "row",
    flex: 1,
    marginRight: 12,
  },
  companyAvatar: {
    marginRight: 12,
  },
  avatarLabel: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#FFFFFF",
  },
  companyDetails: {
    flex: 1,
  },
  companyName: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginBottom: 2,
  },
  companyLocation: {
    fontSize: 14,
    color: "#A5B4FC",
    marginBottom: 2,
  },
  companyType: {
    fontSize: 12,
    color: "#818CF8",
    textTransform: "capitalize",
  },
  statusChip: {
    borderRadius: 20,
  },
  statusChipText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "600",
  },
  cardStats: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginHorizontal: 20,
    marginBottom: 16,
    paddingVertical: 12,
    backgroundColor: "rgba(0, 0, 0, 0.2)",
    borderRadius: 12,
  },
  statItem: {
    alignItems: "center",
    flex: 1,
  },
  statValue: {
    fontSize: 16,
    fontWeight: "bold",
    marginTop: 4,
  },
  statLabel: {
    fontSize: 12,
    marginTop: 2,
  },
  paymentInfo: {
    borderRadius: 12,
    padding: 12,
    marginHorizontal: 20,
    marginBottom: 16,
  },
  paymentRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  paymentLabel: {
    fontSize: 14,
    fontWeight: "500",
  },
  paymentValue: {
    fontSize: 14,
    fontWeight: "600",
    flex: 1,
    textAlign: "right",
  },
  cardFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 12,
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderTopWidth: 1,
  },
  companyMeta: {
    flex: 1,
  },
  metaText: {
    fontSize: 12,
    marginBottom: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#0F172A",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#A5B4FC",
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#0F172A",
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: "#EF4444",
    marginTop: 16,
    marginBottom: 24,
    textAlign: "center",
  },
  retryButton: {
    backgroundColor: "#1F72A1",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#FFFFFF",
    fontWeight: "600",
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: "#FFFFFF",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 16,
    color: "#A5B4FC",
    textAlign: "center",
  },
});
