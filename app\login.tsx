import {
  View,
  StyleSheet,
  Image,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { TextInput, Button, Text } from "react-native-paper";
import { useState, useCallback } from "react";
import { useRouter } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import { useLoginMutation } from "../generated/graphql";
import { BlurView } from "expo-blur";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useDrawer } from "@/contexts/DrawerContext";

export default function LoginScreen() {
  const router = useRouter();
  const { setIsDrawerOpen } = useDrawer();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [login, { loading }] = useLoginMutation();

  const handleLogin = useCallback(async () => {
    try {
      const result = await login({
        variables: {
          params: { email, password },
        },
      });

      if (result.data?.login) {
        if (result.data.login.error) {
          setError(result.data.login.error.message);
          return;
        }
        await AsyncStorage.setItem("token", result.data.login.token!);
        // Reset drawer state before navigation
        setIsDrawerOpen(false);
        if (result.data.login.user?.company.id === 0) {
          router.replace("/(tabs)/admin");
        } else {
          router.replace("/(tabs)/dashboard");
        }
      } else {
        setError("Login failed. Please try again.");
      }
    } catch (err: any) {
      console.error("Login error:", err);
      setError(err.message || "An unexpected error occurred");
    }
  }, [email, password, login, router, setIsDrawerOpen]);

  const handleEmailChange = useCallback((text: string) => {
    setEmail(text);
  }, []);

  const handlePasswordChange = useCallback((text: string) => {
    setPassword(text);
  }, []);

  return (
    <LinearGradient
      colors={["#1E1B4B", "#1E40AF", "#4C1D95"]}
      style={styles.container}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.keyboardContainer}
      >
        <Image
          source={require("../assets/images/logo.png")}
          style={styles.logo}
          resizeMode="contain"
        />
        <BlurView intensity={90} tint="dark" style={styles.card}>
          <View style={styles.cardContent}>
            <Text variant="headlineSmall" style={styles.title}>
              Welcome Back
            </Text>
            <Text variant="bodyMedium" style={styles.subtitle}>
              Sign in to continue
            </Text>

            <TextInput
              placeholder="Email"
              placeholderTextColor="#D1D5DB"
              value={email}
              onChangeText={handleEmailChange}
              mode="outlined"
              keyboardType="email-address"
              autoCapitalize="none"
              style={styles.input}
              textColor="#FFFFFF"
              theme={{
                colors: {
                  primary: "#60A5FA",
                  background: "transparent",
                },
                roundness: 12,
              }}
              outlineStyle={styles.inputOutline}
            />

            <TextInput
              placeholder="Password"
              placeholderTextColor="#D1D5DB"
              value={password}
              onChangeText={handlePasswordChange}
              mode="outlined"
              secureTextEntry
              style={styles.input}
              textColor="#FFFFFF"
              theme={{
                colors: {
                  primary: "#60A5FA",
                  background: "transparent",
                },
                roundness: 12,
              }}
              outlineStyle={styles.inputOutline}
            />

            {error ? <Text style={styles.error}>{error}</Text> : null}

            <Button
              mode="contained"
              onPress={handleLogin}
              loading={loading}
              disabled={loading}
              style={styles.button}
              contentStyle={styles.buttonContent}
              labelStyle={styles.buttonLabel}
              theme={{ roundness: 12 }}
            >
              Sign In
            </Button>
          </View>
        </BlurView>
      </KeyboardAvoidingView>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  keyboardContainer: {
    flex: 1,
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    padding: 24,
  },
  logo: {
    width: 120,
    height: 120,
    marginBottom: 24,
    tintColor: "#FFFFFF", // Make logo white-tinted if it's not visible
  },
  card: {
    width: "100%",
    maxWidth: 420,
    borderRadius: 24,
    overflow: "hidden",
    backgroundColor: "rgba(0, 0, 0, 0.4)",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  cardContent: {
    paddingVertical: 32,
    paddingHorizontal: 24,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
  },
  title: {
    textAlign: "center",
    fontWeight: "700",
    color: "#FFFFFF",
    marginBottom: 8,
  },
  subtitle: {
    textAlign: "center",
    color: "rgba(255, 255, 255, 0.7)",
    marginBottom: 24,
  },
  input: {
    marginBottom: 16,
    backgroundColor: "transparent",
  },
  inputOutline: {
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.3)",
    backgroundColor: "rgba(255, 255, 255, 0.05)",
  },
  button: {
    borderRadius: 12,
    backgroundColor: "#3B82F6",
    marginTop: 16,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  buttonContent: {
    paddingVertical: 10,
  },
  buttonLabel: {
    fontWeight: "600",
    fontSize: 16,
    color: "#FFFFFF",
  },
  error: {
    color: "#F87171",
    marginBottom: 16,
    textAlign: "center",
    fontSize: 14,
  },
});
