#!/usr/bin/env node
const { generate } = require("@graphql-codegen/cli");
const path = require("path");
const fs = require("fs");

async function generateCode() {
  try {
    console.log("🚀 Generating GraphQL code...");

    // Read and parse the codegen.ts file content
    const configPath = path.join(process.cwd(), "codegen.ts");
    const configContent = fs.readFileSync(configPath, "utf8");

    // Extract the configuration object using regex
    const configMatch = configContent.match(
      /const config: CodegenConfig = ({[\s\S]*?});/
    );
    if (!configMatch) {
      throw new Error("Could not parse codegen.ts configuration");
    }

    // Evaluate the configuration object
    const config = eval(`(${configMatch[1]})`);

    // Replace the schema URL with localhost when running codegen
    const modifiedConfig = {
      ...config,
      schema: config.schema.replace("************", "localhost"),
    };

    await generate(modifiedConfig);

    console.log("✅ GraphQL code generation complete!");
  } catch (error) {
    console.error("❌ Error generating GraphQL code:", error);
    process.exit(1);
  }
}

// Call the function after it's defined
generateCode();
