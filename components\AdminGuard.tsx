import React from "react";
import { View, StyleSheet } from "react-native";
import { Text, ActivityIndicator } from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useMeContext } from "../contexts/MeContext";
import { useEffect } from "react";

interface AdminGuardProps {
  children: React.ReactNode;
}

export function AdminGuard({ children }: AdminGuardProps) {
  const { me, loading } = useMeContext();
  const router = useRouter();

  useEffect(() => {
    // If user is loaded and not an admin, redirect to dashboard
    if (!loading && me && me.company?.id !== 0) {
      router.replace("/(tabs)/dashboard");
    }
  }, [me, loading, router]);

  // Show loading while checking user
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#1F72A1" />
        <Text style={styles.loadingText}>Checking permissions...</Text>
      </View>
    );
  }

  // Show access denied if not admin
  if (!me || me.company?.id !== 0) {
    return (
      <View style={styles.accessDeniedContainer}>
        <MaterialCommunityIcons name="shield-off" size={64} color="#EF4444" />
        <Text style={styles.accessDeniedTitle}>Access Denied</Text>
        <Text style={styles.accessDeniedText}>
          You don't have permission to access this area.
        </Text>
        <Text style={styles.accessDeniedSubtext}>
          Admin access is required to view this page.
        </Text>
      </View>
    );
  }

  // Render children if user is admin
  return <>{children}</>;
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F1F5F9",
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: "#64748B",
  },
  accessDeniedContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F1F5F9",
    padding: 20,
  },
  accessDeniedTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#EF4444",
    marginTop: 16,
    marginBottom: 8,
  },
  accessDeniedText: {
    fontSize: 16,
    color: "#64748B",
    textAlign: "center",
    marginBottom: 8,
  },
  accessDeniedSubtext: {
    fontSize: 14,
    color: "#94A3B8",
    textAlign: "center",
  },
});
