import { useState } from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import { Text, Searchbar, SegmentedButtons } from "react-native-paper";
import {
  Inventory,
  Item,
  useGetMerchandiseItemsQuery,
  useGetSalesQuery,
} from "../../generated/graphql";
import { InventoryList } from "../../components/inventory/InventoryList";
import { SalesInsightList } from "../../components/inventory/SalesInsightList";

export default function InventoryScreen() {
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("inventory");
  const [salesInsightTab, setSalesInsightTab] = useState("most");

  const { data: inventoryData } = useGetMerchandiseItemsQuery({
    fetchPolicy: "network-only",
  });

  const { data: salesData } = useGetSalesQuery({
    fetchPolicy: "network-only",
  });

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text variant="headlineMedium" style={styles.title}>
          Inventory
        </Text>
      </View>

      <View style={styles.tabContainer}>
        <SegmentedButtons
          value={activeTab}
          onValueChange={setActiveTab}
          style={styles.segmentedButtons}
          buttons={[
            {
              value: "inventory",
              label: "Stock",
              style:
                activeTab === "inventory"
                  ? styles.activeSegment
                  : styles.segment,
              labelStyle:
                activeTab === "inventory"
                  ? styles.activeSegmentLabel
                  : styles.segmentLabel,
            },
            {
              value: "sales",
              label: "Sales Insight",
              style:
                activeTab === "sales" ? styles.activeSegment : styles.segment,
              labelStyle:
                activeTab === "sales"
                  ? styles.activeSegmentLabel
                  : styles.segmentLabel,
            },
          ]}
        />
      </View>

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search products"
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          iconColor="rgb(31, 114, 161)"
          placeholderTextColor="#666"
          theme={{
            colors: {
              elevation: { level3: "#ffffff" },
              primary: "rgb(31, 114, 161)",
            },
          }}
        />
      </View>

      {activeTab === "inventory" ? (
        <InventoryList
          data={inventoryData?.getMerchandiseItems as Item[]}
          searchQuery={searchQuery}
          sortByLowStock={true}
        />
      ) : (
        <View>
          <View style={styles.insightTabContainer}>
            <SegmentedButtons
              value={salesInsightTab}
              onValueChange={setSalesInsightTab}
              style={styles.insightSegmentedButtons}
              buttons={[
                {
                  value: "most",
                  label: "Most Sold",
                  style:
                    salesInsightTab === "most"
                      ? styles.activeInsightSegment
                      : styles.insightSegment,
                  labelStyle:
                    salesInsightTab === "most"
                      ? styles.activeInsightSegmentLabel
                      : styles.insightSegmentLabel,
                },
                {
                  value: "least",
                  label: "Not Sold",
                  style:
                    salesInsightTab === "least"
                      ? styles.activeInsightSegment
                      : styles.insightSegment,
                  labelStyle:
                    salesInsightTab === "least"
                      ? styles.activeInsightSegmentLabel
                      : styles.insightSegmentLabel,
                },
              ]}
            />
          </View>
          <SalesInsightList
            salesData={salesData?.getSales as Inventory[]}
            inventoryData={inventoryData?.getMerchandiseItems as Item[]}
            searchQuery={searchQuery}
            showLeastSold={salesInsightTab === "least"}
          />
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    padding: 16,
  },
  title: {
    color: "rgb(31, 114, 161)",
    fontWeight: "bold",
    fontSize: 28,
  },
  tabContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  segmentedButtons: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 4,
  },
  segment: {
    borderRadius: 8,
    borderWidth: 0,
  },
  activeSegment: {
    borderRadius: 8,
    borderWidth: 0,
    backgroundColor: "rgb(31, 114, 161)",
  },
  segmentLabel: {
    color: "#666",
  },
  activeSegmentLabel: {
    color: "#fff",
    fontWeight: "600",
  },
  insightTabContainer: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  insightSegmentedButtons: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 4,
  },
  insightSegment: {
    borderRadius: 8,
    borderWidth: 0,
  },
  activeInsightSegment: {
    borderRadius: 8,
    borderWidth: 0,
    backgroundColor: "rgb(69, 196, 99)",
  },
  insightSegmentLabel: {
    color: "#666",
  },
  activeInsightSegmentLabel: {
    color: "#fff",
    fontWeight: "600",
  },
  searchContainer: {
    padding: 16,
  },
  searchBar: {
    elevation: 0,
    backgroundColor: "#fff",
    borderRadius: 12,
    height: 50,
  },
});
