import { createContext, useContext, ReactNode } from "react";
import { MeQuery, useMeQuery } from "../generated/graphql";

interface MeContextType {
  me?: MeQuery["me"];
  loading: boolean;
}

export const MeContext = createContext<MeContextType>({
  loading: true,
});

export const useMeContext = () => useContext(MeContext);

interface MeProviderProps {
  children: ReactNode;
}

export function MeProvider({ children }: MeProviderProps) {
  const { data, loading } = useMeQuery();

  return (
    <MeContext.Provider
      value={{
        me: data?.me,
        loading,
      }}
    >
      {children}
    </MeContext.Provider>
  );
}
