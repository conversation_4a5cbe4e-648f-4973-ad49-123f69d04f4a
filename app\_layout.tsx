import { Stack } from "expo-router";
import { Apollo<PERSON>rovider } from "@apollo/client";
import { client } from "../lib/apollo";
import { PaperProvider } from "react-native-paper";
import { StatusBar } from "expo-status-bar";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { MeProvider } from "../contexts/MeContext";
import { DrawerProvider } from "@/contexts/DrawerContext";
import AuthGate from "./auth-gate";
import * as SplashScreen from "expo-splash-screen";

const theme = {
  colors: {
    primary: "rgb(31, 114, 161)",
    accent: "#60A5FA",
    background: "#FFFFFF",
    surface: "#FFFFFF",
    text: "#1E1B4B",
    error: "#B00020",
  },
};

// Prevent auto-hiding splash screen
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  return (
    <SafeAreaProvider>
      <ApolloProvider client={client}>
        <PaperProvider theme={theme}>
          <MeProvider>
            <DrawerProvider>
              <AuthGate />
              <StatusBar style="light" />
              <Stack
                screenOptions={{
                  headerShown: false,
                  animation: "none",
                }}
              >
                <Stack.Screen name="index" options={{ animation: "none" }} />
                <Stack.Screen name="(tabs)" options={{ animation: "none" }} />
                <Stack.Screen name="login" options={{ animation: "none" }} />
                <Stack.Screen
                  name="+not-found"
                  options={{ animation: "none" }}
                />
              </Stack>
            </DrawerProvider>
          </MeProvider>
        </PaperProvider>
      </ApolloProvider>
    </SafeAreaProvider>
  );
}
