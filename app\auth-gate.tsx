import { useEffect, useCallback, useRef } from "react";
import { useRouter, usePathname } from "expo-router";
import { useMeQuery } from "../generated/graphql";
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as SplashScreen from "expo-splash-screen";

// Define routes that don't require authentication
const publicRoutes = [
  "/login",
  "/register",
  "/forgot-password",
  "/reset-password",
];

function AuthGate() {
  const router = useRouter();
  const pathname = usePathname();
  const hasNavigated = useRef(false);

  // Skip authentication check for public routes
  const isPublicRoute = publicRoutes.includes(pathname);

  const { data, loading } = useMeQuery({
    fetchPolicy: "network-only",
    skip: isPublicRoute, // Skip the query for public routes
    onError: async () => {
      if (hasNavigated.current || isPublicRoute) return;
      hasNavigated.current = true;
      await AsyncStorage.removeItem("token");
      await SplashScreen.hideAsync();
      router.replace("/login");
    },
  });

  const checkAuth = useCallback(async () => {
    // If we're on a public route, just hide the splash screen and return
    if (isPublicRoute) {
      await SplashScreen.hideAsync();
      return;
    }

    if (hasNavigated.current || loading) return;

    try {
      const token = await AsyncStorage.getItem("token");

      if (!token) {
        hasNavigated.current = true;
        await SplashScreen.hideAsync();
        router.replace("/login");
        return;
      }

      if (data?.me) {
        hasNavigated.current = true;
        await SplashScreen.hideAsync();

        // Redirect based on user type
        if (data.me.company?.id === 0) {
          router.replace("/(tabs)/admin");
        } else {
          router.replace("/(tabs)/dashboard");
        }
      } else if (!loading) {
        hasNavigated.current = true;
        await AsyncStorage.removeItem("token");
        await SplashScreen.hideAsync();
        router.replace("/login");
      }
    } catch (err) {
      if (hasNavigated.current) return;
      hasNavigated.current = true;
      console.error("Auth check error:", err);
      await AsyncStorage.removeItem("token");
      await SplashScreen.hideAsync();
      router.replace("/login");
    }
  }, [loading, data, router, isPublicRoute]);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // Return null to prevent any rendering
  return null;
}

export default AuthGate;
